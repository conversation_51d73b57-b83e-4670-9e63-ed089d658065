<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Export Generator - Online Banking System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .stat-card p {
            opacity: 0.9;
        }
        
        .export-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .export-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .export-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .export-btn:active {
            transform: translateY(0);
        }
        
        .table-selection {
            margin-top: 20px;
        }
        
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .table-checkbox {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .table-checkbox:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .table-checkbox input {
            margin-right: 10px;
        }
        
        .custom-query {
            margin-top: 20px;
        }
        
        .custom-query textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        .custom-query input[type="text"] {
            width: 100%;
            padding: 12px;
            margin-top: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .exports-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
        }
        
        .export-file {
            background: white;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .export-file h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .export-file p {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ SQL Export Generator</h1>
            <p>Online Banking System Database Export Tool</p>
        </div>
        
        <div class="content">
            <!-- Database Statistics -->
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <h3 id="tableCount">-</h3>
                    <p>Tables</p>
                </div>
                <div class="stat-card">
                    <h3 id="dbSize">-</h3>
                    <p>Database Size</p>
                </div>
                <div class="stat-card">
                    <h3 id="userCount">-</h3>
                    <p>Users</p>
                </div>
                <div class="stat-card">
                    <h3 id="transactionCount">-</h3>
                    <p>Transactions</p>
                </div>
            </div>
            
            <!-- Messages -->
            <div id="messageContainer"></div>
            
            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Generating export...</p>
            </div>
            
            <!-- Quick Export Options -->
            <div class="export-section">
                <h2>🚀 Quick Export Options</h2>
                <div class="export-buttons">
                    <button class="export-btn" onclick="generateExport('full_backup')">
                        📦 Full Backup<br><small>Structure + Data</small>
                    </button>
                    <button class="export-btn" onclick="generateExport('schema_backup')">
                        🏗️ Schema Only<br><small>Structure Only</small>
                    </button>
                    <button class="export-btn" onclick="generateExport('data_backup')">
                        📊 Data Only<br><small>Data Without Structure</small>
                    </button>
                    <button class="export-btn" onclick="refreshExports()">
                        🔄 Refresh List<br><small>Update Export List</small>
                    </button>
                </div>
            </div>
            
            <!-- Table Selection Export -->
            <div class="export-section">
                <h2>📋 Select Tables to Export</h2>
                <div class="table-selection">
                    <button class="export-btn" onclick="toggleTableSelection()" id="toggleTablesBtn">
                        Show Table Selection
                    </button>
                    <div class="table-grid hidden" id="tableGrid"></div>
                    <button class="export-btn hidden" onclick="exportSelectedTables()" id="exportTablesBtn">
                        Export Selected Tables
                    </button>
                </div>
            </div>
            
            <!-- Custom Query Export -->
            <div class="export-section">
                <h2>⚡ Custom Query Export</h2>
                <div class="custom-query">
                    <textarea id="customQuery" placeholder="Enter your SQL query here...&#10;Example: SELECT * FROM accounts WHERE status = 'active'"></textarea>
                    <input type="text" id="customFilename" placeholder="Enter filename (optional)">
                    <button class="export-btn" onclick="exportCustomQuery()" style="margin-top: 15px;">
                        Execute & Export Query
                    </button>
                </div>
            </div>
            
            <!-- Existing Exports -->
            <div class="exports-list">
                <h2>📁 Existing SQL Exports</h2>
                <div id="exportsList"></div>
            </div>
        </div>
    </div>

    <script>
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadTables();
            loadExports();
        });
        
        // Load database statistics
        function loadStats() {
            fetch('sql_export_generator.php?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('tableCount').textContent = data.stats.table_count || '-';
                        document.getElementById('dbSize').textContent = (data.stats.size_mb || '0') + ' MB';
                        document.getElementById('userCount').textContent = data.stats.user_count || '-';
                        document.getElementById('transactionCount').textContent = data.stats.transaction_count || '-';
                    }
                })
                .catch(error => console.error('Error loading stats:', error));
        }
        
        // Load available tables
        function loadTables() {
            fetch('sql_export_generator.php?action=get_tables')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const tableGrid = document.getElementById('tableGrid');
                        tableGrid.innerHTML = '';
                        
                        data.tables.forEach(table => {
                            const div = document.createElement('div');
                            div.className = 'table-checkbox';
                            div.innerHTML = `
                                <input type="checkbox" id="table_${table}" value="${table}">
                                <label for="table_${table}">${table}</label>
                            `;
                            tableGrid.appendChild(div);
                        });
                    }
                })
                .catch(error => console.error('Error loading tables:', error));
        }
        
        // Load existing exports
        function loadExports() {
            fetch('sql_export_generator.php?action=list_exports')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const exportsList = document.getElementById('exportsList');
                        exportsList.innerHTML = '';
                        
                        if (data.exports.length === 0) {
                            exportsList.innerHTML = '<p>No SQL exports found.</p>';
                            return;
                        }
                        
                        data.exports.forEach(exportFile => {
                            const div = document.createElement('div');
                            div.className = 'export-file';
                            div.innerHTML = `
                                <h4>${exportFile.name}</h4>
                                <p>Size: ${formatBytes(exportFile.size)} | Modified: ${exportFile.modified}</p>
                            `;
                            exportsList.appendChild(div);
                        });
                    }
                })
                .catch(error => console.error('Error loading exports:', error));
        }
        
        // Generate export
        function generateExport(type) {
            showLoading();
            
            fetch('sql_export_generator.php?action=' + type, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                showMessage(data.message, data.success ? 'success' : 'error');
                if (data.success) {
                    loadExports();
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Toggle table selection
        function toggleTableSelection() {
            const tableGrid = document.getElementById('tableGrid');
            const exportBtn = document.getElementById('exportTablesBtn');
            const toggleBtn = document.getElementById('toggleTablesBtn');
            
            if (tableGrid.classList.contains('hidden')) {
                tableGrid.classList.remove('hidden');
                exportBtn.classList.remove('hidden');
                toggleBtn.textContent = 'Hide Table Selection';
            } else {
                tableGrid.classList.add('hidden');
                exportBtn.classList.add('hidden');
                toggleBtn.textContent = 'Show Table Selection';
            }
        }
        
        // Export selected tables
        function exportSelectedTables() {
            const checkboxes = document.querySelectorAll('#tableGrid input[type="checkbox"]:checked');
            const tables = Array.from(checkboxes).map(cb => cb.value);
            
            if (tables.length === 0) {
                showMessage('Please select at least one table to export.', 'error');
                return;
            }
            
            showLoading();
            
            const formData = new FormData();
            formData.append('action', 'table_backup');
            formData.append('tables', JSON.stringify(tables));
            
            fetch('sql_export_generator.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                showMessage(data.message, data.success ? 'success' : 'error');
                if (data.success) {
                    loadExports();
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Export custom query
        function exportCustomQuery() {
            const query = document.getElementById('customQuery').value.trim();
            const filename = document.getElementById('customFilename').value.trim();
            
            if (!query) {
                showMessage('Please enter a SQL query.', 'error');
                return;
            }
            
            showLoading();
            
            const formData = new FormData();
            formData.append('action', 'custom_export');
            formData.append('query', query);
            if (filename) {
                formData.append('filename', filename);
            }
            
            fetch('sql_export_generator.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                showMessage(data.message, data.success ? 'success' : 'error');
                if (data.success) {
                    loadExports();
                    document.getElementById('customQuery').value = '';
                    document.getElementById('customFilename').value = '';
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Refresh exports list
        function refreshExports() {
            loadExports();
            showMessage('Export list refreshed.', 'success');
        }
        
        // Show loading
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        // Hide loading
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const div = document.createElement('div');
            div.className = `message ${type}`;
            div.textContent = message;
            
            container.innerHTML = '';
            container.appendChild(div);
            
            setTimeout(() => {
                div.remove();
            }, 5000);
        }
        
        // Format bytes
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
