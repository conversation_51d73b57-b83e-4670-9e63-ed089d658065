# production-deploy.ps1 - Windows PowerShell deployment script for Online Banking System
# Version: 1.2.0
# Date: 2025-07-10

param(
    [string]$SourceDir = "C:\MAMP\htdocs\online_banking",
    [string]$DestDir = "C:\inetpub\wwwroot\banking",
    [string]$DbName = "banking_production",
    [string]$DbUser = "banking_user",
    [string]$DbPass = "CHANGE_THIS_PASSWORD",
    [string]$DomainName = "yourdomain.com",
    [switch]$Force
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Blue = "Blue"
$Yellow = "Yellow"

function Write-Header {
    Write-Host "========================================" -ForegroundColor Blue
    Write-Host "   Online Banking Production Deploy" -ForegroundColor Blue
    Write-Host "========================================" -ForegroundColor Blue
    Write-Host ""
}

function Write-Success {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "✗ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠ $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ $Message" -ForegroundColor Blue
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if running as administrator
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    if (-not $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        Write-Error "This script must be run as Administrator"
        exit 1
    }
    
    # Check if source directory exists
    if (-not (Test-Path $SourceDir)) {
        Write-Error "Source directory not found: $SourceDir"
        Write-Info "Please update SourceDir parameter"
        exit 1
    }
    
    # Check required commands
    $commands = @("php", "mysql")
    foreach ($cmd in $commands) {
        if (-not (Get-Command $cmd -ErrorAction SilentlyContinue)) {
            Write-Error "$cmd is not installed or not in PATH"
            exit 1
        }
    }
    
    Write-Success "Prerequisites check passed"
}

function Backup-Existing {
    if (Test-Path $DestDir) {
        Write-Info "Backing up existing installation..."
        $backupFile = "C:\temp\banking_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
        
        # Create temp directory if it doesn't exist
        if (-not (Test-Path "C:\temp")) {
            New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
        }
        
        try {
            Compress-Archive -Path $DestDir -DestinationPath $backupFile -Force
            Write-Success "Backup created: $backupFile"
        }
        catch {
            Write-Warning "Could not create backup: $($_.Exception.Message)"
        }
    }
}

function New-DirectoryStructure {
    Write-Info "Creating directory structure..."
    
    $directories = @(
        $DestDir,
        "$DestDir\uploads",
        "$DestDir\uploads\documents",
        "$DestDir\uploads\cheques",
        "$DestDir\uploads\temp",
        "$DestDir\logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Success "Directory structure created"
}

function Copy-ApplicationFiles {
    Write-Info "Copying application files..."
    
    # Define exclusion patterns
    $excludePatterns = @(
        "test",
        "*_test.php",
        "test_*.php",
        "debug*.php",
        "*.md",
        "demo-images",
        "sql",
        ".git",
        "logs\*.log",
        "uploads\documents\*",
        "uploads\cheques\*",
        "uploads\temp\*"
    )
    
    # Copy files with exclusions
    robocopy $SourceDir $DestDir /E /XD test demo-images sql .git /XF *_test.php test_*.php debug*.php *.md /NFL /NDL /NJH /NJS
    
    Write-Success "Application files copied"
}

function Install-Dependencies {
    Write-Info "Installing dependencies..."
    
    Push-Location $DestDir
    
    try {
        if (Test-Path "composer.phar") {
            & php composer.phar install --no-dev --optimize-autoloader --no-interaction
        }
        elseif (Get-Command composer -ErrorAction SilentlyContinue) {
            & composer install --no-dev --optimize-autoloader --no-interaction
        }
        else {
            Write-Warning "Composer not found, skipping dependency installation"
        }
        
        Write-Success "Dependencies installed"
    }
    catch {
        Write-Warning "Could not install dependencies: $($_.Exception.Message)"
    }
    finally {
        Pop-Location
    }
}

function Set-FilePermissions {
    Write-Info "Setting file permissions..."
    
    try {
        # Set permissions for IIS
        $acl = Get-Acl $DestDir
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
        $acl.SetAccessRule($accessRule)
        Set-Acl -Path $DestDir -AclObject $acl
        
        # Set specific permissions for upload and log directories
        $uploadDirs = @("$DestDir\uploads", "$DestDir\logs")
        foreach ($dir in $uploadDirs) {
            $acl = Get-Acl $dir
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl -Path $dir -AclObject $acl
        }
        
        Write-Success "File permissions set"
    }
    catch {
        Write-Warning "Could not set file permissions: $($_.Exception.Message)"
    }
}

function Setup-Database {
    Write-Info "Setting up database..."
    
    # Prompt for MySQL root password
    $mysqlRootPass = Read-Host "Enter MySQL root password" -AsSecureString
    $mysqlRootPassPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($mysqlRootPass))
    
    try {
        # Create database and user
        $sqlCommands = @"
CREATE DATABASE IF NOT EXISTS $DbName CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DbUser'@'localhost' IDENTIFIED BY '$DbPass';
GRANT SELECT, INSERT, UPDATE, DELETE ON $DbName.* TO '$DbUser'@'localhost';
FLUSH PRIVILEGES;
"@
        
        $sqlCommands | mysql -u root -p$mysqlRootPassPlain
        
        # Import database schema
        Write-Info "Importing database schema..."
        $schemaFiles = @(
            "$DestDir\database\schema.sql",
            "$DestDir\database\create_otp_table.sql",
            "$DestDir\database\create_super_admin_2fa_table.sql",
            "$DestDir\database\create_super_admin_settings.sql",
            "$DestDir\database\create_user_security_settings.sql",
            "$DestDir\database\create_user_documents_table.sql",
            "$DestDir\database\secure_deletion_tables.sql",
            "$DestDir\database\add_user_fields.sql"
        )
        
        foreach ($file in $schemaFiles) {
            if (Test-Path $file) {
                Get-Content $file | mysql -u $DbUser -p$DbPass $DbName
            }
        }
        
        Write-Success "Database setup completed"
    }
    catch {
        Write-Error "Database setup failed: $($_.Exception.Message)"
        exit 1
    }
}

function Update-Configuration {
    Write-Info "Updating configuration files..."
    
    try {
        # Backup original config files
        Copy-Item "$DestDir\config\config.php" "$DestDir\config\config.php.backup" -Force
        Copy-Item "$DestDir\config\database.php" "$DestDir\config\database.php.backup" -Force
        Copy-Item "$DestDir\config\email.php" "$DestDir\config\email.php.backup" -Force
        
        # Update config.php
        $configContent = Get-Content "$DestDir\config\config.php" -Raw
        $configContent = $configContent -replace "define\('DEBUG_MODE', true\)", "define('DEBUG_MODE', false)"
        $configContent = $configContent -replace "define\('ERROR_REPORTING', true\)", "define('ERROR_REPORTING', false)"
        $configContent = $configContent -replace "define\('BASE_URL', 'http://localhost/online_banking/'\)", "define('BASE_URL', 'https://$DomainName/')"
        Set-Content "$DestDir\config\config.php" -Value $configContent
        
        # Update database.php
        $dbContent = Get-Content "$DestDir\config\database.php" -Raw
        $dbContent = $dbContent -replace "define\('DB_NAME', 'online_banking'\)", "define('DB_NAME', '$DbName')"
        $dbContent = $dbContent -replace "define\('DB_USERNAME', 'root'\)", "define('DB_USERNAME', '$DbUser')"
        $dbContent = $dbContent -replace "define\('DB_PASSWORD', 'root'\)", "define('DB_PASSWORD', '$DbPass')"
        Set-Content "$DestDir\config\database.php" -Value $dbContent
        
        Write-Success "Configuration files updated"
        Write-Warning "Please manually update email configuration in config\email.php"
    }
    catch {
        Write-Error "Configuration update failed: $($_.Exception.Message)"
    }
}

function New-BackupScript {
    Write-Info "Creating backup script..."
    
    $backupScript = @"
# banking-backup.ps1 - Automated backup script
`$BackupDir = "C:\Backups\Banking"
`$Date = Get-Date -Format "yyyyMMdd_HHmmss"

if (-not (Test-Path `$BackupDir)) {
    New-Item -ItemType Directory -Path `$BackupDir -Force | Out-Null
}

# Backup database
mysqldump -u $DbUser -p'$DbPass' $DbName > "`$BackupDir\db_backup_`$Date.sql"

# Backup files
Compress-Archive -Path "$DestDir" -DestinationPath "`$BackupDir\files_backup_`$Date.zip" -Force

# Keep only last 7 days of backups
Get-ChildItem `$BackupDir -Name "*.sql" | Where-Object { `$_.LastWriteTime -lt (Get-Date).AddDays(-7) } | Remove-Item
Get-ChildItem `$BackupDir -Name "*.zip" | Where-Object { `$_.LastWriteTime -lt (Get-Date).AddDays(-7) } | Remove-Item
"@
    
    Set-Content "C:\Scripts\banking-backup.ps1" -Value $backupScript
    
    # Create scheduled task for daily backups
    try {
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\banking-backup.ps1"
        $trigger = New-ScheduledTaskTrigger -Daily -At "2:00AM"
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
        Register-ScheduledTask -TaskName "Banking Backup" -Action $action -Trigger $trigger -Settings $settings -Force
        
        Write-Success "Backup script created and scheduled"
    }
    catch {
        Write-Warning "Could not create scheduled task: $($_.Exception.Message)"
    }
}

function Show-PostDeployment {
    Write-Header
    Write-Success "Deployment completed successfully!"
    Write-Host ""
    Write-Info "Application deployed to: $DestDir"
    Write-Info "Database created: $DbName"
    Write-Info "Database user: $DbUser"
    Write-Host ""
    Write-Warning "IMPORTANT: Complete these manual steps:"
    Write-Host "1. Update config\email.php with production SMTP settings"
    Write-Host "2. Change default admin passwords via the application"
    Write-Host "3. Configure IIS site for $DomainName"
    Write-Host "4. Install and configure SSL certificate"
    Write-Host "5. Test the application thoroughly"
    Write-Host "6. Set up monitoring and alerting"
    Write-Host ""
    Write-Info "Default admin credentials (CHANGE IMMEDIATELY):"
    Write-Host "   Super Admin: superadmin / Admin@123"
    Write-Host "   Admin: admin / admin123"
    Write-Host ""
    Write-Info "Backup script installed: C:\Scripts\banking-backup.ps1"
    Write-Info "Daily backups scheduled at 2:00 AM"
    Write-Host ""
    Write-Success "Your online banking system is ready for production!"
}

function Main {
    Write-Header
    
    Write-Info "Starting deployment process..."
    Write-Info "Source: $SourceDir"
    Write-Info "Destination: $DestDir"
    Write-Info "Database: $DbName"
    Write-Info "Domain: $DomainName"
    Write-Host ""
    
    # Confirm deployment
    if (-not $Force) {
        $confirm = Read-Host "Continue with deployment? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Info "Deployment cancelled"
            exit 0
        }
    }
    
    # Execute deployment steps
    Test-Prerequisites
    Backup-Existing
    New-DirectoryStructure
    Copy-ApplicationFiles
    Install-Dependencies
    Set-FilePermissions
    Setup-Database
    Update-Configuration
    New-BackupScript
    Show-PostDeployment
}

# Run main function
Main
