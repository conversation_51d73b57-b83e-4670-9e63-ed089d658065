# Online Banking System - Production Deployment Package

**Version:** 1.2.0  
**Date:** 2025-07-10  
**Target Environment:** Production  

## 📋 PRODUCTION FILE MANIFEST

### ✅ CORE APPLICATION FILES (REQUIRED)

#### Root Level Files
```
index.php                    # Main application entry point
login.php                    # User login page
logout.php                   # Logout handler
register.php                 # User registration
composer.json                # Composer dependencies
composer.phar                # Composer executable (if needed)
```

#### Configuration Files (REQUIRES MODIFICATION)
```
config/
├── config.php              # Main configuration (MODIFY: disable debug, change URLs)
├── database.php             # Database connection (MODIFY: production credentials)
├── email.php                # Email configuration (MODIFY: production SMTP)
├── email_templates.php      # Email templates
├── super_admin_settings.php # Super admin configuration
├── dashboard_functions.php  # Dashboard utilities
├── AuditLogger.php          # Audit logging
├── EmailManager.php         # Email management
├── ErrorHandler.php         # Error handling
├── ErrorLogger.php          # Error logging
├── InputValidator.php       # Input validation
└── SessionManager.php       # Session management
```

#### Admin Panel (COMPLETE)
```
admin/
├── index.php               # Admin dashboard
├── login.php               # Admin login
├── logout.php              # Admin logout
├── users.php               # User management
├── transactions.php        # Transaction management
├── transfers.php           # Transfer management
├── pending-users.php       # Pending user approvals
├── add-user.php            # Add new users
├── edit-user.php           # Edit user details
├── view-user.php           # View user details
├── view-user-new.php       # Enhanced user view
├── activate-user.php       # User activation
├── approve-user.php        # User approval
├── reject-user.php         # User rejection
├── suspend-user.php        # User suspension
├── delete-user.php         # User deletion
├── delete-user-new.php     # Enhanced user deletion
├── secure-delete-user.php  # Secure deletion
├── credit-debit.php        # Account operations
├── generate-card.php       # Virtual card generation
├── generate-crypto-wallet.php # Crypto wallet generation
├── virtual-cards.php       # Card management
├── card-applications.php   # Card applications
├── card-operations.php     # Card operations
├── card-transactions.php   # Card transactions
├── crypto-wallets.php      # Crypto wallet management
├── crypto-applications.php # Crypto applications
├── crypto-operations.php   # Crypto operations
├── crypto-transactions.php # Crypto transactions
├── btc-integration.php     # Bitcoin integration
├── cheque-deposits.php     # Cheque processing
├── bank-account-settings.php # Account settings
├── security-settings.php  # Security configuration
├── user-security-management.php # User security
├── user-status-management.php # User status
├── configure-2fa.php       # 2FA configuration
├── google-2fa-setup.php    # Google 2FA setup
├── generate-otp.php        # OTP generation
├── verify-otp.php          # OTP verification
├── check-otp.php           # OTP checking
├── email-settings.php      # Email configuration
├── email-logs.php          # Email logging
├── email-debug.php         # Email debugging
├── smtp-settings.php       # SMTP configuration
├── super-admin-settings.php # Super admin settings
├── transaction-details.php # Transaction details
├── update-transaction.php  # Transaction updates
├── update-transaction-status.php # Status updates
├── update-pending-status.php # Pending status updates
├── update-database.php     # Database updates
├── export-transactions.php # Transaction export
├── logs.php                # System logs
├── setup.php               # Initial setup
├── test-password-change.php # Password testing
├── dashboard/              # Admin dashboard components
│   ├── index.php
│   ├── script.js
│   └── style.css
├── accounts/               # Account management
│   ├── index.php
│   ├── script.js
│   └── style.css
├── settings/               # Admin settings
│   ├── index.php
│   ├── script.js
│   └── style.css
├── virtual-cards/          # Virtual card management
│   ├── index.php
│   ├── script.js
│   └── style.css
├── users/                  # User management interface
├── includes/               # Admin includes
│   ├── admin-header.php
│   ├── admin-footer.php
│   ├── admin-sidebar.php
│   ├── user-data-loader.php
│   └── user-documents-section.php
├── ajax/                   # AJAX endpoints
│   ├── generate-otp.php
│   ├── get_user_details.php
│   ├── get_user_security.php
│   ├── get_security_history.php
│   ├── get_user_document_details.php
│   ├── get-otp-history.php
│   ├── get-cheque-details.php
│   ├── upload-document.php
│   ├── view-document.php
│   ├── update-document-status.php
│   └── delete-document.php
└── js/                     # Admin JavaScript
    └── document-management.js
```

#### Super Admin Panel (COMPLETE)
```
super-admin/
├── index.php               # Super admin entry
├── login.php               # Super admin login
├── logout.php              # Super admin logout
├── dashboard.php           # Super admin dashboard
├── user-management.php     # User management
├── system-settings.php     # System configuration
├── system-settings-fixed.php # Fixed system settings
├── system-settings-full.php # Full system settings
├── system-settings-simple.php # Simple system settings
├── site-settings.php       # Site configuration
├── appearance-settings.php # Appearance settings
├── smtp-config.php         # SMTP configuration
├── email-templates.php     # Email template management
├── preview-template.php    # Template preview
├── security.php            # Security settings
├── audit-logs.php          # Audit log viewer
├── setup-2fa.php           # 2FA setup
├── 2fa-setup.php           # Alternative 2FA setup
├── verify-2fa.php          # 2FA verification
├── reset-2fa-lock.php      # 2FA reset
├── debug-2fa.php           # 2FA debugging
├── test-2fa-code.php       # 2FA testing
├── test-page.php           # Testing page
├── test-stats.php          # Statistics testing
└── includes/               # Super admin includes
    ├── header.php
    ├── footer.php
    ├── auth.php
    └── 2fa-functions.php
```

#### User Dashboard (COMPLETE)
```
dashboard/
├── index.php               # Main dashboard
├── index-clean.php         # Clean dashboard version
├── wallet.php              # Wallet details
├── payments.php            # Payments page
├── cards.php               # Cards page
├── insights.php            # Financial insights
├── invoices.php            # Invoices and statements
├── rewards.php             # Rewards system
├── feedback.php            # User feedback
├── help.php                # Help and support
├── settings.php            # User settings
├── script.js               # Dashboard JavaScript
├── style.css               # Dashboard styles
├── accounts/               # Account management
│   └── index.php
├── payments/               # Payment system
│   └── index.php
├── cards/                  # Card management
│   └── index.php
├── security/               # Security center
│   └── index.php
├── statements/             # Financial statements
│   └── index.php
├── transactions/           # Transaction history
│   └── index.php
├── transfers/              # Transfer management
│   └── index.php
├── beneficiaries/          # Beneficiary management
│   └── index.php
└── ajax/                   # AJAX endpoints
    └── create_card.php
```

#### Authentication System (COMPLETE)
```
auth/
├── extend-session.php      # Session extension
├── logout.php              # Logout handler
├── verify-otp.php          # OTP verification
├── login-style.css         # Login styles
├── otp-style.css           # OTP styles
├── includes/               # Auth includes
│   ├── login_header.php
│   ├── login_form.php
│   ├── login_footer.php
│   └── login_logic.php
└── styles/                 # Additional styles
    └── login.css
```

#### Templates and Includes (COMPLETE)
```
templates/
└── user/                   # User templates
    ├── header.php
    ├── footer.php
    └── sidebar.php

includes/
├── header.php              # Global header
├── footer.php              # Global footer
├── sidebar.php             # Global sidebar
├── admin_header.php        # Admin header
├── admin_header_new.php    # New admin header
├── user_header.php         # User header
├── user_header_new.php     # New user header
├── user-2fa-functions.php  # User 2FA functions
├── dashboard/              # Dashboard includes
│   ├── main.php
│   └── footer.php
└── components/             # Reusable components
    ├── user-header.php
    ├── user-account-info.php
    ├── user-personal-info.php
    ├── user-financial-section.php
    ├── user-security-section.php
    ├── user-cards-crypto-section.php
    ├── user-overview-cards.php
    └── user-modals.php
```

#### Assets (REQUIRED)
```
assets/
├── css/                    # Stylesheets
│   ├── dashboard.css
│   ├── dashboard-clean.css
│   ├── user-dashboard.css
│   ├── components.css
│   ├── cards.css
│   ├── transactions.css
│   └── transfers.css
├── js/                     # JavaScript files
│   ├── dashboard.js
│   ├── user-dashboard.js
│   ├── cards.js
│   ├── transactions.js
│   └── transfers.js
├── admin/                  # Admin assets
│   ├── admin.css
│   ├── admin.js
│   ├── css/
│   │   └── view-user.css
│   └── js/
│       └── view-user.js
├── user/                   # User assets
│   ├── css/
│   └── js/
├── uploads/                # Upload directory
│   ├── favicon.png
│   ├── favicon.svg
│   ├── logo.png
│   └── logo.svg
└── img/                    # Images directory
```

#### Database Files (REQUIRED)
```
database/
├── schema.sql              # Main database schema
├── create_otp_table.sql    # OTP table creation
├── create_super_admin_2fa_table.sql # Super admin 2FA
├── create_super_admin_settings.sql # Super admin settings
├── create_transactions_table.sql # Transactions table
├── create_user_documents_table.sql # User documents
├── create_user_security_settings.sql # User security
├── secure_deletion_tables.sql # Secure deletion
└── add_user_fields.sql     # Additional user fields
```

#### Vendor Dependencies (REQUIRED)
```
vendor/                     # Composer dependencies
├── autoload.php            # Composer autoloader
├── composer/               # Composer files
├── phpmailer/              # PHPMailer library
├── pragmarx/               # Google2FA library
└── [other dependencies]    # Additional packages
```

#### Upload Directories (CREATE WITH PERMISSIONS)
```
uploads/                    # Main upload directory (755)
├── documents/              # User documents (755)
├── cheques/                # Cheque uploads (755)
└── temp/                   # Temporary files (755)

logs/                       # Log directory (755)
├── .gitkeep               # Keep directory in git
├── audit.log              # Audit logs (will be created)
├── error.log              # Error logs (will be created)
└── email_*.log            # Email logs (will be created)
```

### ❌ EXCLUDE FROM PRODUCTION (DEVELOPMENT/TESTING FILES)

#### Test Files and Directories
```
test/                       # Complete test directory
test_*.php                  # All test files
*_test.php                  # All test files
admin_add_user_test.php     # Admin test file
banking_email_test.php      # Email test file
basic_test.php              # Basic test file
create_test_user.php        # Test user creation
phpmailer_test_*.php        # PHPMailer tests
simple_email_test*.php      # Email tests
test_*.php                  # All test files
working_email_test.php      # Email test file
final_login_test.php        # Login test file
test_login*.php             # Login tests
test_session_debug.php      # Session tests
test_smtp_connection.php    # SMTP tests
test_user_creation*.php     # User creation tests
test_email_*.php            # Email tests
test_2fa_*.php              # 2FA tests
test-login.php              # Login test
```

#### Debug and Development Files
```
debug*.php                  # All debug files
debug-*.php                 # Debug files
admin_otp_guide.php         # Development guide
clear_session.php           # Session clearing
extend-session.php          # Session extension
fix_passwords.php           # Password fixing
force_smtp_test.php         # SMTP testing
get_otp.php                 # OTP getting
check-user-otp.php          # OTP checking
view_email_log.php          # Email log viewing
direct_email_test.php       # Direct email test
standalone_smtp_test*.php   # SMTP tests
simple_improvements_test.php # Improvement tests
install_phpmailer.php       # PHPMailer installation
```

#### Setup and Migration Files
```
setup_*.php                 # All setup files
update_*.php                # Update scripts
composer.bat                # Composer batch file (Windows only)
```

#### Documentation Files
```
*.md                        # All markdown files
task_breakdown.md           # Task breakdown
project_status.md           # Project status
online_banking_prd.md       # Product requirements
LOGIN_PAGE_IMPROVEMENTS_SUMMARY.md # Improvements
Online_Banking_Dashboard_Improvements_Implementation__*.md # Implementation docs
PLAYWRIGHT_ANALYSIS_FIXES.md # Analysis fixes
SUPER_ADMIN_2FA_README.md   # 2FA readme
dashboard/modularization_plan.md # Modularization plan
sql/README.md               # SQL readme
```

#### Demo and Example Files
```
demo-images/                # Demo images directory
auth/enhanced_visual_demo.html # Visual demo
auth/login_improvements_demo.html # Login demo
```

#### SQL Export Tools (OPTIONAL - for production maintenance)
```
sql_export_generator.php    # SQL export generator
sql_export_interface.php    # SQL export interface
sql/                        # SQL export directory
├── export_database.bat     # Windows export script
├── export_database.sh      # Linux export script
├── *.sql                   # Generated SQL files
└── README.md               # SQL documentation
```

#### Empty Placeholder Directories
```
accounts/                   # Empty directory
beneficiaries/              # Empty directory
cards/                      # Empty directory
payments/                   # Empty directory
security/                   # Empty directory
statements/                 # Empty directory
support/                    # Empty directory
transactions/               # Empty directory
transfers/                  # Empty directory
```

## 🔧 PRODUCTION CONFIGURATION REQUIREMENTS

### 1. Configuration Files That MUST Be Modified

#### config/config.php
```php
// CHANGE THESE SETTINGS FOR PRODUCTION:
define('DEBUG_MODE', false);           // MUST be false in production
define('ERROR_REPORTING', false);      // MUST be false in production
define('BASE_URL', 'https://yourdomain.com/'); // Change to production URL
define('SITE_NAME', 'Your Bank Name'); // Change to your bank name
define('ENVIRONMENT', 'production');   // Set to production
```

#### config/database.php
```php
// CHANGE DATABASE CREDENTIALS:
define('DB_HOST', 'your-production-host');     // Production database host
define('DB_USERNAME', 'your-production-user'); // Production database user
define('DB_PASSWORD', 'your-secure-password'); // Strong production password
define('DB_NAME', 'your-production-db');       // Production database name
```

#### config/email.php
```php
// CONFIGURE PRODUCTION SMTP:
define('SMTP_HOST', 'your-smtp-server.com');   // Production SMTP host
define('SMTP_PORT', 587);                      // Production SMTP port
define('SMTP_USERNAME', '<EMAIL>'); // Production email
define('SMTP_PASSWORD', 'your-email-password'); // Production email password
define('FROM_EMAIL', '<EMAIL>'); // Production from email
define('FROM_NAME', 'Your Bank Name');          // Production from name
```

### 2. Default Credentials That MUST Be Changed

#### Super Admin Account
- **Username**: `superadmin` → Change to secure username
- **Password**: `Admin@123` → Change to strong password
- **Email**: Update to production email address

#### Admin Account
- **Username**: `admin` → Change to secure username
- **Password**: `admin123` → Change to strong password
- **Email**: Update to production email address

#### Test User Accounts
- **Remove or secure all test accounts**
- **Change default passwords for any retained accounts**

### 3. Security Configurations

#### File Permissions (Linux/Unix)
```bash
# Application files
find /path/to/app -type f -exec chmod 644 {} \;
find /path/to/app -type d -exec chmod 755 {} \;

# Configuration files (more restrictive)
chmod 600 config/*.php

# Upload directories
chmod 755 uploads/
chmod 755 uploads/documents/
chmod 755 uploads/cheques/
chmod 755 uploads/temp/

# Log directory
chmod 755 logs/
chmod 644 logs/*.log

# Executable files
chmod 755 composer.phar
```

#### Web Server Configuration

##### Apache (.htaccess)
```apache
# Deny access to sensitive files
<Files "*.php~">
    Deny from all
</Files>
<Files "*.log">
    Deny from all
</Files>
<Files "composer.*">
    Deny from all
</Files>

# Deny access to config directory
<Directory "config">
    Deny from all
</Directory>

# Deny access to logs directory
<Directory "logs">
    Deny from all
</Directory>

# Enable HTTPS redirect
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

##### Nginx Configuration
```nginx
# Deny access to sensitive files
location ~ \.(log|md)$ {
    deny all;
}

location /config/ {
    deny all;
}

location /logs/ {
    deny all;
}

location /test/ {
    deny all;
}

# Force HTTPS
if ($scheme != "https") {
    return 301 https://$host$request_uri;
}
```

### 4. Database Configuration

#### Production Database Setup
```sql
-- Create production database
CREATE DATABASE your_production_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create production user with limited privileges
CREATE USER 'prod_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE ON your_production_db.* TO 'prod_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Import Database Schema
```bash
mysql -u prod_user -p your_production_db < database/schema.sql
mysql -u prod_user -p your_production_db < database/create_otp_table.sql
mysql -u prod_user -p your_production_db < database/create_super_admin_2fa_table.sql
mysql -u prod_user -p your_production_db < database/create_super_admin_settings.sql
mysql -u prod_user -p your_production_db < database/create_user_security_settings.sql
mysql -u prod_user -p your_production_db < database/create_user_documents_table.sql
mysql -u prod_user -p your_production_db < database/secure_deletion_tables.sql
```

### 5. SSL/TLS Configuration

#### SSL Certificate Requirements
- **Valid SSL certificate** from trusted CA
- **Strong cipher suites** (TLS 1.2+)
- **HSTS headers** enabled
- **Secure cookie settings** in PHP

#### PHP Session Security
```php
// Add to config/config.php
ini_set('session.cookie_secure', 1);     // HTTPS only
ini_set('session.cookie_httponly', 1);   // No JavaScript access
ini_set('session.use_strict_mode', 1);   // Strict session mode
ini_set('session.cookie_samesite', 'Strict'); // CSRF protection
```

## 📦 PRODUCTION DEPLOYMENT CHECKLIST

### Phase 1: Pre-Deployment Preparation

#### 1.1 Environment Setup
- [ ] **Production server** with PHP 7.4+ and MySQL 5.7+
- [ ] **SSL certificate** installed and configured
- [ ] **Domain name** configured and pointing to server
- [ ] **Email server** configured for SMTP
- [ ] **Backup system** in place

#### 1.2 Security Preparation
- [ ] **Strong passwords** generated for all accounts
- [ ] **Database credentials** created with limited privileges
- [ ] **SMTP credentials** configured for production
- [ ] **File permissions** planned and documented
- [ ] **Firewall rules** configured

#### 1.3 Configuration Preparation
- [ ] **Production configuration files** prepared
- [ ] **Database connection** tested
- [ ] **Email configuration** tested
- [ ] **SSL certificate** verified
- [ ] **Domain configuration** verified

### Phase 2: File Deployment

#### 2.1 Create Production Directory Structure
```bash
# Create main application directory
mkdir -p /var/www/html/banking

# Create required subdirectories
mkdir -p /var/www/html/banking/{uploads/{documents,cheques,temp},logs}

# Set initial permissions
chown -R www-data:www-data /var/www/html/banking
chmod -R 755 /var/www/html/banking
```

#### 2.2 Copy Core Application Files
```bash
# Copy core files (exclude test and debug files)
rsync -av --exclude='test/' \
          --exclude='*_test.php' \
          --exclude='test_*.php' \
          --exclude='debug*.php' \
          --exclude='*.md' \
          --exclude='demo-images/' \
          /source/online_banking/ /var/www/html/banking/
```

#### 2.3 Install Dependencies
```bash
cd /var/www/html/banking
php composer.phar install --no-dev --optimize-autoloader
```

#### 2.4 Set File Permissions
```bash
# Set application permissions
find /var/www/html/banking -type f -exec chmod 644 {} \;
find /var/www/html/banking -type d -exec chmod 755 {} \;

# Secure configuration files
chmod 600 /var/www/html/banking/config/*.php

# Set upload directory permissions
chmod 755 /var/www/html/banking/uploads/
chmod 755 /var/www/html/banking/uploads/documents/
chmod 755 /var/www/html/banking/uploads/cheques/
chmod 755 /var/www/html/banking/uploads/temp/

# Set log directory permissions
chmod 755 /var/www/html/banking/logs/

# Set ownership
chown -R www-data:www-data /var/www/html/banking
```

### Phase 3: Database Setup

#### 3.1 Create Production Database
```bash
mysql -u root -p << EOF
CREATE DATABASE banking_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'banking_user'@'localhost' IDENTIFIED BY 'STRONG_PASSWORD_HERE';
GRANT SELECT, INSERT, UPDATE, DELETE ON banking_production.* TO 'banking_user'@'localhost';
FLUSH PRIVILEGES;
EOF
```

#### 3.2 Import Database Schema
```bash
cd /var/www/html/banking
mysql -u banking_user -p banking_production < database/schema.sql
mysql -u banking_user -p banking_production < database/create_otp_table.sql
mysql -u banking_user -p banking_production < database/create_super_admin_2fa_table.sql
mysql -u banking_user -p banking_production < database/create_super_admin_settings.sql
mysql -u banking_user -p banking_production < database/create_user_security_settings.sql
mysql -u banking_user -p banking_production < database/create_user_documents_table.sql
mysql -u banking_user -p banking_production < database/secure_deletion_tables.sql
mysql -u banking_user -p banking_production < database/add_user_fields.sql
```

### Phase 4: Configuration

#### 4.1 Update Configuration Files
```bash
# Backup original config files
cp config/config.php config/config.php.backup
cp config/database.php config/database.php.backup
cp config/email.php config/email.php.backup

# Edit configuration files with production settings
nano config/config.php      # Update DEBUG_MODE, BASE_URL, etc.
nano config/database.php    # Update database credentials
nano config/email.php       # Update SMTP settings
```

#### 4.2 Create Production Admin Account
```bash
# Access the application and create secure admin accounts
# Or use SQL to update default accounts:
mysql -u banking_user -p banking_production << EOF
UPDATE accounts SET
    username = 'your_secure_admin_username',
    password = '$2y$10$your_hashed_password_here',
    email = '<EMAIL>'
WHERE username = 'admin';

UPDATE accounts SET
    username = 'your_secure_superadmin_username',
    password = '$2y$10$your_hashed_password_here',
    email = '<EMAIL>'
WHERE username = 'superadmin';
EOF
```

### Phase 5: Web Server Configuration

#### 5.1 Apache Virtual Host
```apache
<VirtualHost *:443>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/html/banking

    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key

    <Directory /var/www/html/banking>
        AllowOverride All
        Require all granted
    </Directory>

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains; preload"

    # Hide server information
    ServerTokens Prod
    ServerSignature Off

    ErrorLog ${APACHE_LOG_DIR}/banking_error.log
    CustomLog ${APACHE_LOG_DIR}/banking_access.log combined
</VirtualHost>

# Redirect HTTP to HTTPS
<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    Redirect permanent / https://yourdomain.com/
</VirtualHost>
```

#### 5.2 Nginx Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/html/banking;
    index index.php index.html;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;

    # Deny access to sensitive files
    location ~ \.(log|md|txt)$ {
        deny all;
    }

    location /config/ {
        deny all;
    }

    location /logs/ {
        deny all;
    }

    location /test/ {
        deny all;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

### Phase 6: Testing and Verification

#### 6.1 Basic Functionality Tests
- [ ] **Homepage loads** without errors
- [ ] **User login** works with test account
- [ ] **Admin login** works with admin account
- [ ] **Super admin login** works with super admin account
- [ ] **Database connection** is working
- [ ] **Email sending** is functional
- [ ] **File uploads** work correctly
- [ ] **SSL certificate** is valid and working

#### 6.2 Security Tests
- [ ] **HTTPS redirect** is working
- [ ] **Sensitive files** are not accessible (config/, logs/, test/)
- [ ] **Error reporting** is disabled
- [ ] **Debug mode** is disabled
- [ ] **Default passwords** have been changed
- [ ] **File permissions** are correct
- [ ] **Security headers** are present

#### 6.3 Performance Tests
- [ ] **Page load times** are acceptable
- [ ] **Database queries** are optimized
- [ ] **Static files** are cached properly
- [ ] **Memory usage** is within limits

### Phase 7: Post-Deployment

#### 7.1 Monitoring Setup
```bash
# Set up log rotation
cat > /etc/logrotate.d/banking << EOF
/var/www/html/banking/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

#### 7.2 Backup Configuration
```bash
# Create backup script
cat > /usr/local/bin/banking-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups/banking"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
mysqldump -u banking_user -p'PASSWORD' banking_production > $BACKUP_DIR/db_backup_$DATE.sql

# Backup files
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz -C /var/www/html banking

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /usr/local/bin/banking-backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /usr/local/bin/banking-backup.sh" | crontab -
```

#### 7.3 Maintenance Tasks
- [ ] **Regular backups** scheduled
- [ ] **Log monitoring** configured
- [ ] **Security updates** planned
- [ ] **Performance monitoring** set up
- [ ] **SSL certificate renewal** scheduled

## 🚀 AUTOMATED DEPLOYMENT SCRIPT

Create this script to automate the deployment process:

```bash
#!/bin/bash
# production-deploy.sh - Automated deployment script

set -e  # Exit on any error

# Configuration
SOURCE_DIR="/path/to/development/online_banking"
DEST_DIR="/var/www/html/banking"
DB_NAME="banking_production"
DB_USER="banking_user"
DB_PASS="STRONG_PASSWORD_HERE"

echo "=== Online Banking Production Deployment ==="
echo "Starting deployment process..."

# Phase 1: Backup existing installation
if [ -d "$DEST_DIR" ]; then
    echo "Backing up existing installation..."
    tar -czf "/tmp/banking_backup_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$(dirname $DEST_DIR)" "$(basename $DEST_DIR)"
fi

# Phase 2: Create directory structure
echo "Creating directory structure..."
mkdir -p $DEST_DIR/{uploads/{documents,cheques,temp},logs}

# Phase 3: Copy files (excluding development files)
echo "Copying application files..."
rsync -av --delete \
    --exclude='test/' \
    --exclude='*_test.php' \
    --exclude='test_*.php' \
    --exclude='debug*.php' \
    --exclude='*.md' \
    --exclude='demo-images/' \
    --exclude='sql/' \
    --exclude='.git/' \
    $SOURCE_DIR/ $DEST_DIR/

# Phase 4: Install dependencies
echo "Installing dependencies..."
cd $DEST_DIR
php composer.phar install --no-dev --optimize-autoloader

# Phase 5: Set permissions
echo "Setting file permissions..."
find $DEST_DIR -type f -exec chmod 644 {} \;
find $DEST_DIR -type d -exec chmod 755 {} \;
chmod 600 $DEST_DIR/config/*.php
chmod 755 $DEST_DIR/uploads/{documents,cheques,temp}
chmod 755 $DEST_DIR/logs/
chown -R www-data:www-data $DEST_DIR

# Phase 6: Database setup
echo "Setting up database..."
mysql -u root -p << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT SELECT, INSERT, UPDATE, DELETE ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

# Import database schema
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/schema.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_otp_table.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_super_admin_2fa_table.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_super_admin_settings.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_user_security_settings.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_user_documents_table.sql
mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/secure_deletion_tables.sql

echo "=== Deployment Complete ==="
echo "Please complete the following manual steps:"
echo "1. Update config/config.php with production settings"
echo "2. Update config/database.php with production credentials"
echo "3. Update config/email.php with production SMTP settings"
echo "4. Change default admin passwords"
echo "5. Configure web server virtual host"
echo "6. Test the application"
echo ""
echo "Application deployed to: $DEST_DIR"
echo "Database created: $DB_NAME"
echo "Database user: $DB_USER"
```

## ✅ FINAL PRODUCTION CHECKLIST

### Pre-Launch Verification
- [ ] All configuration files updated for production
- [ ] Default passwords changed
- [ ] SSL certificate installed and working
- [ ] Database connection tested
- [ ] Email functionality tested
- [ ] File permissions set correctly
- [ ] Security headers configured
- [ ] Error reporting disabled
- [ ] Debug mode disabled
- [ ] Backup system configured

### Security Verification
- [ ] Sensitive files not accessible via web
- [ ] Strong passwords implemented
- [ ] HTTPS enforced
- [ ] Security headers present
- [ ] File upload restrictions in place
- [ ] Database user has minimal privileges
- [ ] Log files protected
- [ ] Session security configured

### Performance Verification
- [ ] Page load times acceptable
- [ ] Database queries optimized
- [ ] Static file caching enabled
- [ ] Memory usage within limits
- [ ] Log rotation configured

### Monitoring Setup
- [ ] Error logging enabled
- [ ] Access logging enabled
- [ ] Backup automation configured
- [ ] SSL certificate monitoring
- [ ] Disk space monitoring
- [ ] Performance monitoring

---

**Deployment Status**: Ready for Production
**Estimated Deployment Time**: 2-4 hours
**Required Expertise**: System Administrator + PHP Developer
**Support**: Comprehensive documentation and testing included
