O:33:"TheSeer\Tokenizer\TokenCollection":2:{s:41:" TheSeer\Tokenizer\TokenCollection tokens";a:3:{i:0;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:1;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:2;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:3;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}}s:38:" TheSeer\Tokenizer\TokenCollection pos";N;}