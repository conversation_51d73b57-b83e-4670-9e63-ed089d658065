<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInit4a95e00ea7ad92343342ae04a051d5b5
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInit4a95e00ea7ad92343342ae04a051d5b5', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInit4a95e00ea7ad92343342ae04a051d5b5', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInit4a95e00ea7ad92343342ae04a051d5b5::getInitializer($loader));

        $loader->setClassMapAuthoritative(true);
        $loader->setApcuPrefix('8d9229413d6aecbb1be8');
        $loader->register(true);

        return $loader;
    }
}
