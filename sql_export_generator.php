<?php
/**
 * SQL Export Generator for Online Banking System
 * This script generates SQL exports of the database
 * 
 * Usage: Run this script via web browser or command line
 * Web: http://localhost/online_banking/sql_export_generator.php
 * CLI: php sql_export_generator.php
 */

// Include database configuration
require_once 'config/database.php';

class SQLExportGenerator {
    private $db;
    private $exportDir;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->exportDir = __DIR__ . '/sql/';
        
        // Create sql directory if it doesn't exist
        if (!is_dir($this->exportDir)) {
            mkdir($this->exportDir, 0755, true);
        }
    }
    
    /**
     * Generate full database backup (structure + data)
     */
    public function generateFullBackup($filename = null) {
        if (!$filename) {
            $filename = 'full_backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $command = sprintf(
            '"%s" -u %s -p%s --single-transaction --routines --triggers %s > "%s"',
            $this->getMysqldumpPath(),
            DB_USERNAME,
            DB_PASSWORD,
            DB_NAME,
            $this->exportDir . $filename
        );
        
        return $this->executeCommand($command, $filename);
    }
    
    /**
     * Generate schema only backup (structure without data)
     */
    public function generateSchemaBackup($filename = null) {
        if (!$filename) {
            $filename = 'schema_only_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $command = sprintf(
            '"%s" -u %s -p%s --no-data --single-transaction --routines --triggers %s > "%s"',
            $this->getMysqldumpPath(),
            DB_USERNAME,
            DB_PASSWORD,
            DB_NAME,
            $this->exportDir . $filename
        );
        
        return $this->executeCommand($command, $filename);
    }
    
    /**
     * Generate data only backup (data without structure)
     */
    public function generateDataBackup($filename = null) {
        if (!$filename) {
            $filename = 'data_only_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $command = sprintf(
            '"%s" -u %s -p%s --no-create-info --single-transaction %s > "%s"',
            $this->getMysqldumpPath(),
            DB_USERNAME,
            DB_PASSWORD,
            DB_NAME,
            $this->exportDir . $filename
        );
        
        return $this->executeCommand($command, $filename);
    }
    
    /**
     * Generate backup for specific tables
     */
    public function generateTableBackup($tables, $filename = null) {
        if (!$filename) {
            $filename = 'tables_' . implode('_', $tables) . '_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $tablesStr = implode(' ', $tables);
        
        $command = sprintf(
            '"%s" -u %s -p%s --single-transaction %s %s > "%s"',
            $this->getMysqldumpPath(),
            DB_USERNAME,
            DB_PASSWORD,
            DB_NAME,
            $tablesStr,
            $this->exportDir . $filename
        );
        
        return $this->executeCommand($command, $filename);
    }
    
    /**
     * Generate custom SQL export based on query
     */
    public function generateCustomExport($query, $filename) {
        try {
            $result = $this->db->query($query);
            $content = "-- Custom SQL Export\n";
            $content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
            $content .= "-- Query: " . $query . "\n\n";
            
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $content .= "-- " . json_encode($row) . "\n";
                }
            }
            
            $filepath = $this->exportDir . $filename;
            file_put_contents($filepath, $content);
            
            return [
                'success' => true,
                'message' => "Custom export saved to: $filename",
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating custom export: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get list of all tables in the database
     */
    public function getTables() {
        try {
            $result = $this->db->query("SHOW TABLES");
            $tables = [];
            
            while ($row = $result->fetch_array()) {
                $tables[] = $row[0];
            }
            
            return $tables;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get database statistics
     */
    public function getDatabaseStats() {
        try {
            $stats = [];
            
            // Get table count
            $result = $this->db->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'");
            $stats['table_count'] = $result->fetch_assoc()['table_count'];
            
            // Get database size
            $result = $this->db->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'db_size_mb' FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'");
            $stats['size_mb'] = $result->fetch_assoc()['db_size_mb'];
            
            // Get user count
            $result = $this->db->query("SELECT COUNT(*) as user_count FROM accounts");
            $stats['user_count'] = $result->fetch_assoc()['user_count'];
            
            // Get transaction count
            $result = $this->db->query("SELECT COUNT(*) as transaction_count FROM transactions");
            $stats['transaction_count'] = $result->fetch_assoc()['transaction_count'];
            
            return $stats;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * List existing SQL export files
     */
    public function listExports() {
        $files = [];
        $directory = new DirectoryIterator($this->exportDir);
        
        foreach ($directory as $file) {
            if ($file->isFile() && $file->getExtension() === 'sql') {
                $files[] = [
                    'name' => $file->getFilename(),
                    'size' => $file->getSize(),
                    'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                    'path' => $file->getPathname()
                ];
            }
        }
        
        // Sort by modification time (newest first)
        usort($files, function($a, $b) {
            return strtotime($b['modified']) - strtotime($a['modified']);
        });
        
        return $files;
    }
    
    /**
     * Get mysqldump executable path
     */
    private function getMysqldumpPath() {
        // For MAMP on Windows
        $mampPath = 'C:\\MAMP\\bin\\mysql\\bin\\mysqldump.exe';
        if (file_exists($mampPath)) {
            return $mampPath;
        }
        
        // For XAMPP on Windows
        $xamppPath = 'C:\\xampp\\mysql\\bin\\mysqldump.exe';
        if (file_exists($xamppPath)) {
            return $xamppPath;
        }
        
        // Default system path
        return 'mysqldump';
    }
    
    /**
     * Execute shell command
     */
    private function executeCommand($command, $filename) {
        $output = [];
        $returnCode = 0;
        
        exec($command . ' 2>&1', $output, $returnCode);
        
        $filepath = $this->exportDir . $filename;
        
        if ($returnCode === 0 && file_exists($filepath)) {
            return [
                'success' => true,
                'message' => "Export saved successfully to: $filename",
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Export failed: ' . implode("\n", $output)
            ];
        }
    }
}

// Handle web requests
if (isset($_GET['action']) || isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $generator = new SQLExportGenerator();
    $action = $_GET['action'] ?? $_POST['action'];
    
    switch ($action) {
        case 'full_backup':
            $result = $generator->generateFullBackup();
            break;
            
        case 'schema_backup':
            $result = $generator->generateSchemaBackup();
            break;
            
        case 'data_backup':
            $result = $generator->generateDataBackup();
            break;
            
        case 'table_backup':
            $tables = $_POST['tables'] ?? [];
            if (empty($tables)) {
                $result = ['success' => false, 'message' => 'No tables selected'];
            } else {
                $result = $generator->generateTableBackup($tables);
            }
            break;
            
        case 'custom_export':
            $query = $_POST['query'] ?? '';
            $filename = $_POST['filename'] ?? 'custom_export_' . date('Y-m-d_H-i-s') . '.sql';
            if (empty($query)) {
                $result = ['success' => false, 'message' => 'No query provided'];
            } else {
                $result = $generator->generateCustomExport($query, $filename);
            }
            break;
            
        case 'list_exports':
            $result = ['success' => true, 'exports' => $generator->listExports()];
            break;
            
        case 'get_tables':
            $result = ['success' => true, 'tables' => $generator->getTables()];
            break;
            
        case 'get_stats':
            $result = ['success' => true, 'stats' => $generator->getDatabaseStats()];
            break;
            
        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }
    
    echo json_encode($result);
    exit;
}

// CLI usage
if (php_sapi_name() === 'cli') {
    $generator = new SQLExportGenerator();
    
    echo "=== Online Banking SQL Export Generator ===\n";
    echo "1. Full Backup (Structure + Data)\n";
    echo "2. Schema Only (Structure)\n";
    echo "3. Data Only\n";
    echo "4. List Existing Exports\n";
    echo "5. Database Statistics\n";
    echo "Enter choice (1-5): ";
    
    $choice = trim(fgets(STDIN));
    
    switch ($choice) {
        case '1':
            $result = $generator->generateFullBackup();
            break;
        case '2':
            $result = $generator->generateSchemaBackup();
            break;
        case '3':
            $result = $generator->generateDataBackup();
            break;
        case '4':
            $exports = $generator->listExports();
            echo "\nExisting SQL Exports:\n";
            foreach ($exports as $export) {
                echo "- {$export['name']} ({$export['size']} bytes) - {$export['modified']}\n";
            }
            exit;
        case '5':
            $stats = $generator->getDatabaseStats();
            echo "\nDatabase Statistics:\n";
            echo "- Tables: {$stats['table_count']}\n";
            echo "- Size: {$stats['size_mb']} MB\n";
            echo "- Users: {$stats['user_count']}\n";
            echo "- Transactions: {$stats['transaction_count']}\n";
            exit;
        default:
            echo "Invalid choice\n";
            exit;
    }
    
    if ($result['success']) {
        echo "Success: {$result['message']}\n";
        echo "File size: {$result['size']} bytes\n";
    } else {
        echo "Error: {$result['message']}\n";
    }
}
?>
