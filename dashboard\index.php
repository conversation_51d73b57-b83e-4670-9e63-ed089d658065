<?php
// Set page variables
$page_title = 'Dashboard';
$additional_css = ['dashboard-clean.css'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get comprehensive user account information
    $user_sql = "SELECT a.*,
                        COALESCE(a.balance, 0) as balance,
                        DATE_FORMAT(a.created_at, '%M %Y') as member_since,
                        DATEDIFF(NOW(), a.created_at) as days_member
                 FROM accounts a
                 WHERE a.id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Get current balance
    $current_balance = $user['balance'] ?? 0;

    // Get virtual card information
    $virtual_card_sql = "SELECT * FROM virtual_cards WHERE user_id = ? AND status = 'active' ORDER BY created_at DESC LIMIT 1";
    $virtual_card_result = $db->query($virtual_card_sql, [$user_id]);
    $virtual_card = $virtual_card_result ? $virtual_card_result->fetch_assoc() : null;

    // Get last outflow transaction (most recent debit)
    $last_outflow_sql = "SELECT * FROM account_transactions WHERE account_id = ? AND transaction_type = 'debit' ORDER BY created_at DESC LIMIT 1";
    $last_outflow_result = $db->query($last_outflow_sql, [$user_id]);
    $last_outflow = $last_outflow_result ? $last_outflow_result->fetch_assoc() : null;

    // Get recent user-initiated transfers (last 5)
    $transfers_sql = "SELECT t.*,
                             CASE
                                 WHEN t.sender_id = ? THEN 'sent'
                                 WHEN t.recipient_id = ? THEN 'received'
                                 ELSE 'unknown'
                             END as transfer_direction
                      FROM transfers t
                      WHERE (t.sender_id = ? OR t.recipient_id = ?)
                      ORDER BY t.created_at DESC LIMIT 5";
    $transfers_result = $db->query($transfers_sql, [$user_id, $user_id, $user_id, $user_id]);
    $recent_transfers = [];
    if ($transfers_result) {
        while ($row = $transfers_result->fetch_assoc()) {
            $recent_transfers[] = $row;
        }
    }

    // Get monthly stats from account_transactions (admin-created)
    $current_month = date('Y-m');
    $monthly_stats_sql = "SELECT
        SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
        COUNT(*) as transaction_count
        FROM account_transactions
        WHERE account_id = ? AND DATE_FORMAT(created_at, '%Y-%m') = ?";

    $stats_result = $db->query($monthly_stats_sql, [$user_id, $current_month]);
    $monthly_stats = $stats_result->fetch_assoc();

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => ''];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = 0;
    $recent_transfers = [];
    $virtual_card = null;
    $last_outflow = null;
}

?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="h3 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>!</h1>
                    <p class="text-muted">Here's your banking overview for today.</p>
                </div>
            </div>

            <!-- Enhanced Account Overview -->
            <div class="row mb-4">
                <!-- Virtual Card Display -->
                <div class="col-lg-4 mb-4">
                    <?php if ($virtual_card): ?>
                        <div class="virtual-card-container">
                            <div class="virtual-card" style="background: linear-gradient(135deg,
                                <?php
                                $card_colors = [
                                    'visa' => '#1a1f71, #2d3a8c',
                                    'mastercard' => '#eb001b, #ff5f00',
                                    'amex' => '#006fcf, #0099cc'
                                ];
                                echo $card_colors[$virtual_card['card_type']] ?? '#1a1f71, #2d3a8c';
                                ?>);">

                                <!-- Card Background Pattern -->
                                <div class="card-pattern"></div>

                                <!-- Card Header -->
                                <div class="card-header-section">
                                    <div class="card-brand-logo">
                                        <?php
                                        $brand_icons = [
                                            'visa' => 'fab fa-cc-visa',
                                            'mastercard' => 'fab fa-cc-mastercard',
                                            'amex' => 'fab fa-cc-amex'
                                        ];
                                        ?>
                                        <i class="<?php echo $brand_icons[$virtual_card['card_type']] ?? 'fab fa-cc-visa'; ?>"></i>
                                    </div>
                                    <div class="card-chip">
                                        <div class="chip-lines"></div>
                                    </div>
                                </div>

                                <!-- Card Number -->
                                <div class="card-number-section">
                                    <div class="card-number">
                                        <?php
                                        $masked_number = substr($virtual_card['card_number'], 0, 4) . ' **** **** ' . substr($virtual_card['card_number'], -4);
                                        echo $masked_number;
                                        ?>
                                    </div>
                                </div>

                                <!-- Card Footer -->
                                <div class="card-footer-section">
                                    <div class="card-holder">
                                        <div class="label">CARD HOLDER</div>
                                        <div class="name"><?php echo strtoupper(htmlspecialchars($virtual_card['card_holder_name'])); ?></div>
                                    </div>
                                    <div class="card-expiry">
                                        <div class="label">EXPIRES</div>
                                        <div class="date"><?php echo sprintf('%02d/%02d', $virtual_card['expiry_month'], $virtual_card['expiry_year'] % 100); ?></div>
                                    </div>
                                    <div class="card-balance">
                                        <div class="label">BALANCE</div>
                                        <div class="amount">$<?php echo number_format($virtual_card['current_balance'], 2); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="no-card-placeholder">
                            <div class="placeholder-content">
                                <i class="fas fa-credit-card"></i>
                                <h6>No Virtual Card</h6>
                                <p>Request a virtual card to start making secure online payments</p>
                                <a href="cards/" class="btn btn-primary btn-sm">Request Card</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Account Balance & Overview -->
                <div class="col-lg-4 mb-4">
                    <div class="account-overview-card">
                        <div class="overview-header">
                            <h5>Account Balance</h5>
                            <span class="account-type"><?php echo ucfirst($user['account_type'] ?? 'Savings'); ?></span>
                        </div>
                        <div class="balance-display">
                            <div class="balance-amount">$<?php echo number_format($current_balance, 2); ?></div>
                            <div class="balance-details">
                                <small>Account: <?php echo htmlspecialchars($user['account_number'] ?? ''); ?></small>
                            </div>
                        </div>

                        <?php if ($last_outflow): ?>
                        <div class="last-transaction">
                            <div class="transaction-label">Last Outflow</div>
                            <div class="transaction-info">
                                <span class="amount">-$<?php echo number_format($last_outflow['amount'], 2); ?></span>
                                <span class="date"><?php echo date('M j, Y', strtotime($last_outflow['created_at'])); ?></span>
                            </div>
                            <div class="transaction-desc"><?php echo htmlspecialchars($last_outflow['description'] ?? 'Transaction'); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Monthly Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="row h-100">
                        <div class="col-12 mb-3">
                            <div class="stats-card h-100">
                                <div class="stats-icon success">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_credits, 2); ?></div>
                                <div class="stats-label">Credits This Month</div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="stats-card h-100">
                                <div class="stats-icon warning">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_debits, 2); ?></div>
                                <div class="stats-label">Debits This Month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="mb-3">Quick Actions</h5>
                    <div class="quick-actions">
                        <a href="transfers/" class="action-btn">
                            <div class="action-icon" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-color);">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Transfer Money</div>
                                <small class="text-muted">Send money to others</small>
                            </div>
                        </a>
                        <a href="payments/" class="action-btn">
                            <div class="action-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Pay Bills</div>
                                <small class="text-muted">Pay your bills online</small>
                            </div>
                        </a>
                        <a href="cards/" class="action-btn">
                            <div class="action-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Manage Cards</div>
                                <small class="text-muted">View and manage cards</small>
                            </div>
                        </a>
                        <a href="statements/" class="action-btn">
                            <div class="action-icon" style="background: rgba(139, 69, 19, 0.1); color: #8b4513;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Transaction Statements</div>
                                <small class="text-muted">Admin transactions</small>
                            </div>
                        </a>
                        <a href="wallet.php" class="action-btn">
                            <div class="action-icon" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Wallet Details</div>
                                <small class="text-muted">Complete overview</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Transfer History -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">Recent Transfer History</h5>
                                <div class="btn-group">
                                    <a href="transfers/" class="btn btn-outline-primary btn-sm">View All Transfers</a>
                                    <a href="statements/" class="btn btn-outline-secondary btn-sm">Transaction Statements</a>
                                </div>
                            </div>

                            <?php if (empty($recent_transfers)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-exchange-alt text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                                    <p class="text-muted mt-3">No transfers yet</p>
                                    <a href="transfers/" class="btn btn-primary btn-sm">Make Your First Transfer</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_transfers as $transfer): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon" style="background-color: <?php echo $transfer['transfer_direction'] === 'sent' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(16, 185, 129, 0.1)'; ?>; color: <?php echo $transfer['transfer_direction'] === 'sent' ? 'var(--danger-color)' : 'var(--success-color)'; ?>;">
                                            <i class="fas fa-<?php echo $transfer['transfer_direction'] === 'sent' ? 'arrow-up' : 'arrow-down'; ?>"></i>
                                        </div>
                                        <div class="transaction-details">
                                            <div class="transaction-title">
                                                <?php if ($transfer['transfer_direction'] === 'sent'): ?>
                                                    Transfer to <?php echo htmlspecialchars($transfer['recipient_name'] ?? $transfer['recipient_account']); ?>
                                                <?php else: ?>
                                                    Transfer from <?php echo htmlspecialchars($transfer['recipient_name'] ?? 'External Account'); ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="transaction-date"><?php echo date('M j, Y', strtotime($transfer['created_at'])); ?></div>
                                            <div class="transaction-status">
                                                <span class="badge bg-<?php echo $transfer['status'] === 'completed' ? 'success' : ($transfer['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo ucfirst($transfer['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="transaction-amount">
                                            <span class="amount <?php echo $transfer['transfer_direction'] === 'sent' ? 'negative' : 'positive'; ?>">
                                                <?php echo $transfer['transfer_direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transfer['amount'], 2); ?>
                                            </span>
                                            <div class="transfer-type">
                                                <small class="text-muted"><?php echo ucfirst($transfer['transfer_type']); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
</div>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>


