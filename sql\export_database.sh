#!/bin/bash

# SQL Export Shell Script for Online Banking System
# This script provides easy access to database export functions

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}   Online Banking SQL Export Tool${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if PHP is available
check_php() {
    if ! command -v php &> /dev/null; then
        print_error "PHP is not found in PATH"
        print_info "Please make sure PHP is installed and accessible"
        exit 1
    fi
}

# Navigate to parent directory
cd "$(dirname "$0")/.."

# Check current directory
if [ ! -f "sql_export_generator.php" ]; then
    print_error "sql_export_generator.php not found in current directory"
    print_info "Current directory: $(pwd)"
    exit 1
fi

# Main menu function
show_menu() {
    clear
    print_header
    print_info "Current directory: $(pwd)"
    echo
    echo "Choose an export option:"
    echo
    echo "1. Full Database Backup (Structure + Data)"
    echo "2. Schema Only (Structure)"
    echo "3. Data Only"
    echo "4. List Existing Exports"
    echo "5. Database Statistics"
    echo "6. Open Web Interface"
    echo "7. Exit"
    echo
}

# Function to handle user input
handle_choice() {
    case $1 in
        1)
            echo
            print_info "Generating full database backup..."
            echo
            echo "1" | php sql_export_generator.php
            ;;
        2)
            echo
            print_info "Generating schema-only backup..."
            echo
            echo "2" | php sql_export_generator.php
            ;;
        3)
            echo
            print_info "Generating data-only backup..."
            echo
            echo "3" | php sql_export_generator.php
            ;;
        4)
            echo
            print_info "Listing existing exports..."
            echo
            echo "4" | php sql_export_generator.php
            ;;
        5)
            echo
            print_info "Database statistics..."
            echo
            echo "5" | php sql_export_generator.php
            ;;
        6)
            echo
            print_info "Opening web interface..."
            echo
            # Try different browsers
            if command -v xdg-open &> /dev/null; then
                xdg-open "http://localhost/online_banking/sql_export_interface.php"
            elif command -v open &> /dev/null; then
                open "http://localhost/online_banking/sql_export_interface.php"
            elif command -v start &> /dev/null; then
                start "http://localhost/online_banking/sql_export_interface.php"
            else
                print_warning "Could not open browser automatically"
                print_info "Please open: http://localhost/online_banking/sql_export_interface.php"
            fi
            print_success "Web interface should open in your default browser"
            ;;
        7)
            echo
            print_success "Thank you for using the SQL Export Tool!"
            echo
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please select 1-7."
            ;;
    esac
}

# Main script execution
main() {
    # Check prerequisites
    check_php
    
    # Main loop
    while true; do
        show_menu
        read -p "Enter your choice (1-7): " choice
        
        handle_choice "$choice"
        
        if [ "$choice" != "7" ]; then
            echo
            print_info "Press Enter to return to menu..."
            read
        fi
    done
}

# Make script executable
chmod +x "$0" 2>/dev/null

# Run main function
main
