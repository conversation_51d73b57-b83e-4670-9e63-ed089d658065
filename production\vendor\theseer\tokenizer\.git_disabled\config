[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/theseer/tokenizer.git
	fetch = +refs/heads/*:refs/remotes/origin/*
	pushurl = **************:theseer/tokenizer.git
[branch "master"]
	remote = origin
	merge = refs/heads/master
[remote "composer"]
	url = https://github.com/theseer/tokenizer.git
	fetch = +refs/heads/*:refs/remotes/composer/*
