<?xml version="1.0"?>
<source xmlns="custom:xml:namespace">
 <line no="1">
  <token name="T_OPEN_TAG">&lt;?php </token>
  <token name="T_DECLARE">declare</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_STRING">strict_types</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_EQUAL">=</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_LNUMBER">1</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="2">
  <token name="T_NAMESPACE">namespace</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">foo</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="3"/>
 <line no="4">
  <token name="T_CLASS">class</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">bar</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_OPEN_CURLY">{</token>
 </line>
 <line no="5">
  <token name="T_WHITESPACE">    </token>
  <token name="T_CONST">const</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">x</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_EQUAL">=</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_CONSTANT_ENCAPSED_STRING">'abc'</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="6"/>
 <line no="7">
  <token name="T_WHITESPACE">    </token>
  <token name="T_DOC_COMMENT">/** @var int */</token>
 </line>
 <line no="8">
  <token name="T_WHITESPACE">    </token>
  <token name="T_PRIVATE">private</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_VARIABLE">$y</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_EQUAL">=</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_LNUMBER">1</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="9"/>
 <line no="10">
  <token name="T_WHITESPACE">    </token>
  <token name="T_PUBLIC">public</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_FUNCTION">function</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">__construct</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_OPEN_CURLY">{</token>
 </line>
 <line no="11">
  <token name="T_WHITESPACE">        </token>
  <token name="T_COMMENT">// do something</token>
 </line>
 <line no="12">
  <token name="T_WHITESPACE">    </token>
  <token name="T_CLOSE_CURLY">}</token>
 </line>
 <line no="13"/>
 <line no="14">
  <token name="T_WHITESPACE">    </token>
  <token name="T_PUBLIC">public</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_FUNCTION">function</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">getY</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_COLON">:</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">int</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_OPEN_CURLY">{</token>
 </line>
 <line no="15">
  <token name="T_WHITESPACE">        </token>
  <token name="T_RETURN">return</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_VARIABLE">$this</token>
  <token name="T_OBJECT_OPERATOR">-&gt;</token>
  <token name="T_STRING">y</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="16">
  <token name="T_WHITESPACE">    </token>
  <token name="T_CLOSE_CURLY">}</token>
 </line>
 <line no="17"/>
 <line no="18">
  <token name="T_WHITESPACE">    </token>
  <token name="T_PUBLIC">public</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_FUNCTION">function</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">getSomeX</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_COLON">:</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">string</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_OPEN_CURLY">{</token>
 </line>
 <line no="19">
  <token name="T_WHITESPACE">        </token>
  <token name="T_RETURN">return</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">self</token>
  <token name="T_DOUBLE_COLON">::</token>
  <token name="T_STRING">x</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="20">
  <token name="T_WHITESPACE">    </token>
  <token name="T_CLOSE_CURLY">}</token>
 </line>
 <line no="21"/>
 <line no="22">
  <token name="T_WHITESPACE">    </token>
  <token name="T_PUBLIC">public</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_FUNCTION">function</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">some</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_STRING">bar</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_VARIABLE">$b</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_COLON">:</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_STRING">string</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_OPEN_CURLY">{</token>
 </line>
 <line no="23">
  <token name="T_WHITESPACE">        </token>
  <token name="T_RETURN">return</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_VARIABLE">$b</token>
  <token name="T_OBJECT_OPERATOR">-&gt;</token>
  <token name="T_STRING">getSomeX</token>
  <token name="T_OPEN_BRACKET">(</token>
  <token name="T_CLOSE_BRACKET">)</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_DOT">.</token>
  <token name="T_WHITESPACE"> </token>
  <token name="T_CONSTANT_ENCAPSED_STRING">'-def'</token>
  <token name="T_SEMICOLON">;</token>
 </line>
 <line no="24">
  <token name="T_WHITESPACE">    </token>
  <token name="T_CLOSE_CURLY">}</token>
 </line>
 <line no="25">
  <token name="T_CLOSE_CURLY">}</token>
 </line>
 <line no="26"/>
</source>
