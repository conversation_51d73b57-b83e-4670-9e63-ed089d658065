<?php
/**
 * Online Banking System - Production Deployment Configuration Tool
 * Version: 1.2.0
 * Date: 2025-07-10
 * 
 * This tool helps configure and verify the production deployment
 * of the Online Banking System.
 */

session_start();

// Security: Password protection
define('INSTALL_PASSWORD', 'banking_install_2025'); // Change this password
define('PRODUCTION_PATH', dirname(__DIR__)); // Root directory where files are uploaded
define('CONFIG_PATH', PRODUCTION_PATH . '/config');

// Check if installation is already completed
if (file_exists(__DIR__ . '/install_completed.lock')) {
    die('<h1>Installation Already Completed</h1><p>The installation has been completed and locked for security. If you need to reconfigure, delete the install_completed.lock file.</p>');
}

// Handle password authentication
if (!isset($_SESSION['install_authenticated'])) {
    if (isset($_POST['install_password'])) {
        if ($_POST['install_password'] === INSTALL_PASSWORD) {
            $_SESSION['install_authenticated'] = true;
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            $auth_error = 'Invalid password. Please try again.';
        }
    }
    
    // Show password form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Online Banking - Installation Authentication</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
            .container { max-width: 500px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c3e50; margin: 0; }
            .header p { color: #7f8c8d; margin: 10px 0 0 0; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
            input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
            .btn { background: #3498db; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; width: 100%; }
            .btn:hover { background: #2980b9; }
            .error { background: #e74c3c; color: white; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .warning { background: #f39c12; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 Online Banking System</h1>
                <p>Production Deployment Configuration</p>
            </div>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong> This installation tool is password-protected. 
                Only authorized personnel should access this configuration interface.
            </div>
            
            <?php if (isset($auth_error)): ?>
                <div class="error"><?php echo htmlspecialchars($auth_error); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="install_password">Installation Password:</label>
                    <input type="password" id="install_password" name="install_password" required 
                           placeholder="Enter installation password">
                </div>
                <button type="submit" class="btn">🔓 Authenticate</button>
            </form>
            
            <div style="margin-top: 30px; padding: 15px; background: #ecf0f1; border-radius: 5px; font-size: 14px;">
                <strong>Default Password:</strong> banking_install_2025<br>
                <strong>Note:</strong> Change this password in the install.php file for security.
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Installation functions
class ProductionInstaller {
    private $production_path;
    private $config_path;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct() {
        $this->production_path = PRODUCTION_PATH;
        $this->config_path = CONFIG_PATH;
    }
    
    public function checkProductionFolder() {
        $required_dirs = [
            'admin', 'super-admin', 'dashboard', 'auth', 'config',
            'database', 'assets', 'uploads', 'logs', 'vendor', 'includes'
        ];

        $required_files = [
            'index.php', 'login.php', 'register.php', 'logout.php'
        ];

        $results = ['dirs' => [], 'files' => [], 'status' => true, 'debug_path' => $this->production_path];

        // Check directories
        foreach ($required_dirs as $dir) {
            $path = $this->production_path . '/' . $dir;
            $exists = is_dir($path);
            $results['dirs'][$dir] = $exists;
            if (!$exists) {
                $results['status'] = false;
                $this->errors[] = "Required directory missing: $dir (checked: $path)";
            }
        }

        // Check files
        foreach ($required_files as $file) {
            $path = $this->production_path . '/' . $file;
            $exists = file_exists($path);
            $results['files'][$file] = $exists;
            if (!$exists) {
                $results['status'] = false;
                $this->errors[] = "Required file missing: $file (checked: $path)";
            }
        }

        // Add debug information about what's actually in the directory
        $actual_contents = [];
        if (is_dir($this->production_path)) {
            $items = scandir($this->production_path);
            foreach ($items as $item) {
                if ($item !== '.' && $item !== '..') {
                    $full_path = $this->production_path . '/' . $item;
                    $actual_contents[] = $item . (is_dir($full_path) ? '/' : '');
                }
            }
        }
        $results['actual_contents'] = $actual_contents;

        return $results;
    }
    
    public function checkDevelopmentFiles() {
        $dev_patterns = [
            '*_test.php', 'test_*.php', 'debug*.php', 'setup_*.php'
        ];
        
        $found_dev_files = [];
        
        foreach ($dev_patterns as $pattern) {
            $files = glob($this->production_path . '/' . $pattern);
            $found_dev_files = array_merge($found_dev_files, $files);
        }
        
        if (!empty($found_dev_files)) {
            $this->warnings[] = "Development files found in production folder";
            return ['status' => false, 'files' => $found_dev_files];
        }
        
        return ['status' => true, 'files' => []];
    }
    
    public function getCurrentDatabaseConfig() {
        $db_config_file = $this->config_path . '/database.php';

        if (!file_exists($db_config_file)) {
            $this->errors[] = "Database configuration file not found at: $db_config_file";
            return false;
        }

        $content = file_get_contents($db_config_file);

        // Extract current values using regex - handle both quoted and unquoted values
        $config = [];

        // Try different patterns for DB_HOST
        if (preg_match("/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]*)['\"].*\)/", $content, $matches)) {
            $config['host'] = $matches[1];
        } elseif (preg_match("/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*([^,\)]+)\s*\)/", $content, $matches)) {
            $config['host'] = trim($matches[1], " '\"");
        }

        // Try different patterns for DB_USERNAME
        if (preg_match("/define\s*\(\s*['\"]DB_USERNAME['\"]\s*,\s*['\"]([^'\"]*)['\"].*\)/", $content, $matches)) {
            $config['username'] = $matches[1];
        } elseif (preg_match("/define\s*\(\s*['\"]DB_USERNAME['\"]\s*,\s*([^,\)]+)\s*\)/", $content, $matches)) {
            $config['username'] = trim($matches[1], " '\"");
        }

        // Try different patterns for DB_NAME
        if (preg_match("/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]*)['\"].*\)/", $content, $matches)) {
            $config['database'] = $matches[1];
        } elseif (preg_match("/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*([^,\)]+)\s*\)/", $content, $matches)) {
            $config['database'] = trim($matches[1], " '\"");
        }

        // Try different patterns for DB_PASSWORD
        if (preg_match("/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]*)['\"].*\)/", $content, $matches)) {
            $config['password'] = $matches[1];
        } elseif (preg_match("/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*([^,\)]+)\s*\)/", $content, $matches)) {
            $config['password'] = trim($matches[1], " '\"");
        }

        // Add debug information
        $config['file_path'] = $db_config_file;
        $config['file_exists'] = true;
        $config['file_readable'] = is_readable($db_config_file);
        $config['file_size'] = filesize($db_config_file);

        return $config;
    }
    
    public function testDatabaseConnection($host, $username, $password, $database) {
        try {
            // First test basic connection without database
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5
            ]);

            // Check if database exists
            $stmt = $pdo->prepare("SHOW DATABASES LIKE ?");
            $stmt->execute([$database]);
            if (!$stmt->fetch()) {
                return [
                    'status' => false,
                    'message' => "Database '$database' does not exist. Please create it first."
                ];
            }

            // Connect to specific database
            $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5
            ]);

            // Test if required tables exist
            $required_tables = ['accounts', 'transactions', 'transfers', 'virtual_cards', 'users'];
            $existing_tables = [];
            $missing_tables = [];

            foreach ($required_tables as $table) {
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if ($stmt->fetch()) {
                    $existing_tables[] = $table;
                } else {
                    $missing_tables[] = $table;
                }
            }

            if (!empty($missing_tables)) {
                return [
                    'status' => false,
                    'message' => 'Database connected but missing tables: ' . implode(', ', $missing_tables) .
                               '. Found tables: ' . implode(', ', $existing_tables) .
                               '. Please import the database schema first.'
                ];
            }

            // Count records in key tables to verify data
            $table_counts = [];
            foreach (['users', 'accounts'] as $table) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                $table_counts[$table] = $result['count'];
            }

            return [
                'status' => true,
                'message' => 'Database connection successful. Found ' . count($existing_tables) . ' required tables. ' .
                           'Users: ' . $table_counts['users'] . ', Accounts: ' . $table_counts['accounts']
            ];

        } catch (PDOException $e) {
            $error_code = $e->getCode();
            $error_message = $e->getMessage();

            // Provide specific error messages for common issues
            if (strpos($error_message, 'Access denied') !== false) {
                return ['status' => false, 'message' => 'Access denied: Invalid username or password'];
            } elseif (strpos($error_message, 'Unknown database') !== false) {
                return ['status' => false, 'message' => "Database '$database' does not exist. Please create it first."];
            } elseif (strpos($error_message, "Can't connect") !== false || strpos($error_message, 'Connection refused') !== false) {
                return ['status' => false, 'message' => "Cannot connect to MySQL server at '$host'. Check if MySQL is running."];
            } else {
                return ['status' => false, 'message' => "Connection failed: $error_message (Code: $error_code)"];
            }
        }
    }
    
    public function updateDatabaseConfig($host, $username, $password, $database) {
        $db_config_file = $this->config_path . '/database.php';
        
        if (!file_exists($db_config_file)) {
            $this->errors[] = "Database configuration file not found";
            return false;
        }
        
        // Backup original file
        copy($db_config_file, $db_config_file . '.backup.' . date('Y-m-d-H-i-s'));
        
        $content = file_get_contents($db_config_file);
        
        // Replace configuration values
        $content = preg_replace("/define\('DB_HOST',\s*'[^']+'\)/", "define('DB_HOST', '$host')", $content);
        $content = preg_replace("/define\('DB_USERNAME',\s*'[^']+'\)/", "define('DB_USERNAME', '$username')", $content);
        $content = preg_replace("/define\('DB_PASSWORD',\s*'[^']+'\)/", "define('DB_PASSWORD', '$password')", $content);
        $content = preg_replace("/define\('DB_NAME',\s*'[^']+'\)/", "define('DB_NAME', '$database')", $content);
        
        if (file_put_contents($db_config_file, $content)) {
            $this->success[] = "Database configuration updated successfully";
            return true;
        } else {
            $this->errors[] = "Failed to update database configuration";
            return false;
        }
    }
    
    public function checkConfigurationFiles() {
        $config_files = [
            'config.php' => 'Main configuration',
            'database.php' => 'Database configuration',
            'email.php' => 'Email configuration',
            'email_templates.php' => 'Email templates'
        ];
        
        $results = [];
        
        foreach ($config_files as $file => $description) {
            $path = $this->config_path . '/' . $file;
            $exists = file_exists($path);
            $results[$file] = [
                'exists' => $exists,
                'description' => $description,
                'readable' => $exists ? is_readable($path) : false,
                'writable' => $exists ? is_writable($path) : false
            ];
            
            if (!$exists) {
                $this->errors[] = "Configuration file missing: $file";
            }
        }
        
        return $results;
    }
    
    public function checkProductionSettings() {
        $config_file = $this->config_path . '/config.php';
        
        if (!file_exists($config_file)) {
            $this->errors[] = "Main configuration file not found";
            return false;
        }
        
        $content = file_get_contents($config_file);
        $settings = [];
        
        // Check error reporting
        if (strpos($content, 'error_reporting(0)') !== false) {
            $settings['error_reporting'] = true;
        } else {
            $settings['error_reporting'] = false;
            $this->warnings[] = "Error reporting should be disabled in production";
        }
        
        // Check debug mode
        if (strpos($content, "define('DEBUG_MODE', false)") !== false) {
            $settings['debug_mode'] = true;
        } else {
            $settings['debug_mode'] = false;
            $this->warnings[] = "Debug mode should be disabled in production";
        }
        
        // Check environment
        if (strpos($content, "define('ENVIRONMENT', 'production')") !== false) {
            $settings['environment'] = true;
        } else {
            $settings['environment'] = false;
            $this->warnings[] = "Environment should be set to 'production'";
        }
        
        return $settings;
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getWarnings() {
        return $this->warnings;
    }
    
    public function getSuccess() {
        return $this->success;
    }
    
    public function markInstallationComplete() {
        $lock_file = __DIR__ . '/install_completed.lock';
        $content = "Installation completed on: " . date('Y-m-d H:i:s') . "\n";
        $content .= "Server: " . $_SERVER['HTTP_HOST'] . "\n";
        $content .= "IP: " . $_SERVER['SERVER_ADDR'] . "\n";
        
        return file_put_contents($lock_file, $content) !== false;
    }
}

// Initialize installer
$installer = new ProductionInstaller();

// Handle form submissions
$form_message = '';
$form_success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_database':
                $host = $_POST['db_host'] ?? '';
                $username = $_POST['db_username'] ?? '';
                $password = $_POST['db_password'] ?? '';
                $database = $_POST['db_name'] ?? '';

                // Test connection first
                $test_result = $installer->testDatabaseConnection($host, $username, $password, $database);

                if ($test_result['status']) {
                    if ($installer->updateDatabaseConfig($host, $username, $password, $database)) {
                        $form_message = 'Database configuration updated successfully!';
                        $form_success = true;
                    } else {
                        $form_message = 'Failed to update database configuration.';
                    }
                } else {
                    $form_message = $test_result['message'];
                }
                break;

            case 'complete_installation':
                $installer->markInstallationComplete();
                $form_message = 'Installation completed successfully!';
                $form_success = true;
                break;
        }
    }
}

// Get current status
$folder_check = $installer->checkProductionFolder();
$dev_files_check = $installer->checkDevelopmentFiles();
$current_db_config = $installer->getCurrentDatabaseConfig();
$config_files = $installer->checkConfigurationFiles();
$production_settings = $installer->checkProductionSettings();

// Test current database connection if config exists
$db_connection_status = false;
if ($current_db_config) {
    $db_test = $installer->testDatabaseConnection(
        $current_db_config['host'] ?? '',
        $current_db_config['username'] ?? '',
        $current_db_config['password'] ?? '',
        $current_db_config['database'] ?? ''
    );
    $db_connection_status = $db_test['status'];
    $db_connection_message = $db_test['message'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Banking - Production Configuration</title>
    <style>
        body { font-family: Arial, sans-serif; background: white; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 800px; margin: 0 auto; background: white; }
        .header { text-align: center; margin-bottom: 40px; padding: 30px 0; border-bottom: 2px solid #f0f0f0; }
        .header h1 { color: #2c3e50; margin: 0 0 10px 0; font-size: 2.2em; }
        .header p { color: #666; margin: 0; font-size: 1.1em; }
        .step { background: white; border: 1px solid #e0e0e0; border-radius: 8px; margin-bottom: 20px; padding: 25px; }
        .step h2 { color: #2c3e50; margin: 0 0 20px 0; font-size: 1.4em; border-bottom: 1px solid #f0f0f0; padding-bottom: 10px; }
        .status { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .form-group { margin-bottom: 20px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        input[type="text"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .btn { background: #007bff; color: white; padding: 12px 25px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #218838; }
        .info-box { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; margin: 15px 0; }
        .info-box h4 { margin: 0 0 10px 0; color: #495057; }
        .check-item { display: flex; align-items: center; padding: 8px 0; }
        .check-icon { margin-right: 10px; font-size: 16px; }
        .check-icon.success { color: #28a745; }
        .check-icon.error { color: #dc3545; }
        .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 20px 0; }
        .progress-bar { background: #28a745; height: 100%; border-radius: 4px; transition: width 0.3s; }
        .text-center { text-align: center; }
        .mt-3 { margin-top: 20px; }
        .mb-3 { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 Online Banking System</h1>
            <p>Production Configuration Tool</p>
        </div>

        <?php if ($form_message): ?>
            <div class="status <?php echo $form_success ? 'success' : 'error'; ?>">
                <?php echo htmlspecialchars($form_message); ?>
            </div>
        <?php endif; ?>

        <!-- Step 1: System Check -->
        <div class="step">
            <h2>Step 1: System Check</h2>

            <?php
            // Calculate overall progress
            $total_checks = 3;
            $passed_checks = 0;

            if ($folder_check['status']) $passed_checks++;
            if ($db_connection_status) $passed_checks++;
            if ($config_files['config.php']['exists']) $passed_checks++;

            $progress_percentage = ($passed_checks / $total_checks) * 100;
            ?>

            <div class="progress mb-3">
                <div class="progress-bar" style="width: <?php echo $progress_percentage; ?>%"></div>
            </div>
            <p class="text-center mb-3"><strong><?php echo round($progress_percentage); ?>% Complete</strong></p>

            <div class="check-item">
                <span class="check-icon <?php echo $folder_check['status'] ? 'success' : 'error'; ?>">
                    <?php echo $folder_check['status'] ? '✅' : '❌'; ?>
                </span>
                <span>Application Files <?php echo $folder_check['status'] ? 'Found' : 'Missing'; ?></span>
            </div>

            <div class="check-item">
                <span class="check-icon <?php echo $db_connection_status ? 'success' : 'error'; ?>">
                    <?php echo $db_connection_status ? '✅' : '❌'; ?>
                </span>
                <span>Database Connection <?php echo $db_connection_status ? 'Working' : 'Failed'; ?></span>
            </div>

            <div class="check-item">
                <span class="check-icon <?php echo $config_files['config.php']['exists'] ? 'success' : 'error'; ?>">
                    <?php echo $config_files['config.php']['exists'] ? '✅' : '❌'; ?>
                </span>
                <span>Configuration Files <?php echo $config_files['config.php']['exists'] ? 'Found' : 'Missing'; ?></span>
            </div>
        </div>

        <!-- Step 2: Database Configuration -->
        <div class="step">
            <h2>Step 2: Database Configuration</h2>

            <?php if ($current_db_config): ?>
                <div class="info-box">
                    <h4>Current Configuration</h4>
                    <p><strong>Host:</strong> <?php echo htmlspecialchars($current_db_config['host'] ?? 'Not set'); ?></p>
                    <p><strong>Database:</strong> <?php echo htmlspecialchars($current_db_config['database'] ?? 'Not set'); ?></p>
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($current_db_config['username'] ?? 'Not set'); ?></p>
                    <p><strong>Status:</strong>
                        <span style="color: <?php echo $db_connection_status ? '#28a745' : '#dc3545'; ?>">
                            <?php echo $db_connection_status ? '✅ Connected' : '❌ Not Connected'; ?>
                        </span>
                    </p>
                </div>
            <?php endif; ?>

            <form method="POST">
                <input type="hidden" name="action" value="update_database">

                <div class="form-row">
                    <div class="form-group">
                        <label for="db_host">Database Host:</label>
                        <input type="text" id="db_host" name="db_host"
                               value="<?php echo htmlspecialchars($current_db_config['host'] ?? 'localhost'); ?>"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="db_name">Database Name:</label>
                        <input type="text" id="db_name" name="db_name"
                               value="<?php echo htmlspecialchars($current_db_config['database'] ?? ''); ?>"
                               required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="db_username">Username:</label>
                        <input type="text" id="db_username" name="db_username"
                               value="<?php echo htmlspecialchars($current_db_config['username'] ?? ''); ?>"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="db_password">Password:</label>
                        <input type="password" id="db_password" name="db_password"
                               value="<?php echo htmlspecialchars($current_db_config['password'] ?? ''); ?>">
                    </div>
                </div>

                <button type="submit" class="btn">Update Database Configuration</button>
            </form>
        </div>

        <!-- Step 3: Complete Installation -->
        <div class="step">
            <h2>Step 3: Complete Installation</h2>

            <?php
            $all_checks_passed = $folder_check['status'] && $db_connection_status && $config_files['config.php']['exists'];
            ?>

            <?php if ($all_checks_passed): ?>
                <div class="status success">
                    <strong>🎉 Ready for Production!</strong><br>
                    All checks have passed. Your banking system is ready to use.
                </div>

                <div class="info-box">
                    <h4>⚠️ Important: Change Default Passwords</h4>
                    <p><strong>Super Admin:</strong> superadmin / Admin@123</p>
                    <p><strong>Admin:</strong> admin / admin123</p>
                </div>

                <form method="POST" class="text-center">
                    <input type="hidden" name="action" value="complete_installation">
                    <button type="submit" class="btn success" onclick="return confirm('Complete installation and lock this tool?')">
                        🔒 Complete Installation
                    </button>
                </form>

            <?php else: ?>
                <div class="status error">
                    <strong>❌ Installation Not Ready</strong><br>
                    Please complete the steps above before finishing installation.
                </div>
            <?php endif; ?>

            <div class="info-box mt-3">
                <h4>🚀 Access Your Banking System</h4>
                <p><strong>User Portal:</strong> <a href="../index.php" target="_blank">../index.php</a></p>
                <p><strong>Admin Panel:</strong> <a href="../admin/" target="_blank">../admin/</a></p>
                <p><strong>Super Admin:</strong> <a href="../super-admin/" target="_blank">../super-admin/</a></p>
            </div>
        </div>
    </div>

</body>
</html>
