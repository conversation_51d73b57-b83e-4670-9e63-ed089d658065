<?php
/**
 * Online Banking System - Production Deployment Configuration Tool
 * Version: 1.2.0
 * Date: 2025-07-10
 * 
 * This tool helps configure and verify the production deployment
 * of the Online Banking System.
 */

session_start();

// Security: Password protection
define('INSTALL_PASSWORD', 'banking_install_2025'); // Change this password
define('PRODUCTION_PATH', dirname(__DIR__) . '/production');
define('CONFIG_PATH', PRODUCTION_PATH . '/config');

// Check if installation is already completed
if (file_exists(__DIR__ . '/install_completed.lock')) {
    die('<h1>Installation Already Completed</h1><p>The installation has been completed and locked for security. If you need to reconfigure, delete the install_completed.lock file.</p>');
}

// Handle password authentication
if (!isset($_SESSION['install_authenticated'])) {
    if (isset($_POST['install_password'])) {
        if ($_POST['install_password'] === INSTALL_PASSWORD) {
            $_SESSION['install_authenticated'] = true;
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            $auth_error = 'Invalid password. Please try again.';
        }
    }
    
    // Show password form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Online Banking - Installation Authentication</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
            .container { max-width: 500px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c3e50; margin: 0; }
            .header p { color: #7f8c8d; margin: 10px 0 0 0; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
            input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
            .btn { background: #3498db; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; width: 100%; }
            .btn:hover { background: #2980b9; }
            .error { background: #e74c3c; color: white; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .warning { background: #f39c12; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 Online Banking System</h1>
                <p>Production Deployment Configuration</p>
            </div>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong> This installation tool is password-protected. 
                Only authorized personnel should access this configuration interface.
            </div>
            
            <?php if (isset($auth_error)): ?>
                <div class="error"><?php echo htmlspecialchars($auth_error); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="install_password">Installation Password:</label>
                    <input type="password" id="install_password" name="install_password" required 
                           placeholder="Enter installation password">
                </div>
                <button type="submit" class="btn">🔓 Authenticate</button>
            </form>
            
            <div style="margin-top: 30px; padding: 15px; background: #ecf0f1; border-radius: 5px; font-size: 14px;">
                <strong>Default Password:</strong> banking_install_2025<br>
                <strong>Note:</strong> Change this password in the install.php file for security.
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Installation functions
class ProductionInstaller {
    private $production_path;
    private $config_path;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct() {
        $this->production_path = PRODUCTION_PATH;
        $this->config_path = CONFIG_PATH;
    }
    
    public function checkProductionFolder() {
        $required_dirs = [
            'admin', 'super-admin', 'dashboard', 'auth', 'config', 
            'database', 'assets', 'uploads', 'logs', 'vendor', 'includes'
        ];
        
        $required_files = [
            'index.php', 'login.php', 'register.php', 'logout.php'
        ];
        
        $results = ['dirs' => [], 'files' => [], 'status' => true];
        
        // Check directories
        foreach ($required_dirs as $dir) {
            $path = $this->production_path . '/' . $dir;
            $exists = is_dir($path);
            $results['dirs'][$dir] = $exists;
            if (!$exists) {
                $results['status'] = false;
                $this->errors[] = "Required directory missing: $dir";
            }
        }
        
        // Check files
        foreach ($required_files as $file) {
            $path = $this->production_path . '/' . $file;
            $exists = file_exists($path);
            $results['files'][$file] = $exists;
            if (!$exists) {
                $results['status'] = false;
                $this->errors[] = "Required file missing: $file";
            }
        }
        
        return $results;
    }
    
    public function checkDevelopmentFiles() {
        $dev_patterns = [
            '*_test.php', 'test_*.php', 'debug*.php', 'setup_*.php'
        ];
        
        $found_dev_files = [];
        
        foreach ($dev_patterns as $pattern) {
            $files = glob($this->production_path . '/' . $pattern);
            $found_dev_files = array_merge($found_dev_files, $files);
        }
        
        if (!empty($found_dev_files)) {
            $this->warnings[] = "Development files found in production folder";
            return ['status' => false, 'files' => $found_dev_files];
        }
        
        return ['status' => true, 'files' => []];
    }
    
    public function getCurrentDatabaseConfig() {
        $db_config_file = $this->config_path . '/database.php';
        
        if (!file_exists($db_config_file)) {
            $this->errors[] = "Database configuration file not found";
            return false;
        }
        
        $content = file_get_contents($db_config_file);
        
        // Extract current values using regex
        $config = [];
        
        if (preg_match("/define\('DB_HOST',\s*'([^']+)'\)/", $content, $matches)) {
            $config['host'] = $matches[1];
        }
        
        if (preg_match("/define\('DB_USERNAME',\s*'([^']+)'\)/", $content, $matches)) {
            $config['username'] = $matches[1];
        }
        
        if (preg_match("/define\('DB_NAME',\s*'([^']+)'\)/", $content, $matches)) {
            $config['database'] = $matches[1];
        }
        
        if (preg_match("/define\('DB_PASSWORD',\s*'([^']+)'\)/", $content, $matches)) {
            $config['password'] = $matches[1];
        }
        
        return $config;
    }
    
    public function testDatabaseConnection($host, $username, $password, $database) {
        try {
            $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            // Test if required tables exist
            $required_tables = ['accounts', 'transactions', 'transfers', 'virtual_cards'];
            $missing_tables = [];
            
            foreach ($required_tables as $table) {
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if (!$stmt->fetch()) {
                    $missing_tables[] = $table;
                }
            }
            
            if (!empty($missing_tables)) {
                return [
                    'status' => false, 
                    'message' => 'Database connected but missing tables: ' . implode(', ', $missing_tables)
                ];
            }
            
            return ['status' => true, 'message' => 'Database connection successful'];
            
        } catch (PDOException $e) {
            return ['status' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
        }
    }
    
    public function updateDatabaseConfig($host, $username, $password, $database) {
        $db_config_file = $this->config_path . '/database.php';
        
        if (!file_exists($db_config_file)) {
            $this->errors[] = "Database configuration file not found";
            return false;
        }
        
        // Backup original file
        copy($db_config_file, $db_config_file . '.backup.' . date('Y-m-d-H-i-s'));
        
        $content = file_get_contents($db_config_file);
        
        // Replace configuration values
        $content = preg_replace("/define\('DB_HOST',\s*'[^']+'\)/", "define('DB_HOST', '$host')", $content);
        $content = preg_replace("/define\('DB_USERNAME',\s*'[^']+'\)/", "define('DB_USERNAME', '$username')", $content);
        $content = preg_replace("/define\('DB_PASSWORD',\s*'[^']+'\)/", "define('DB_PASSWORD', '$password')", $content);
        $content = preg_replace("/define\('DB_NAME',\s*'[^']+'\)/", "define('DB_NAME', '$database')", $content);
        
        if (file_put_contents($db_config_file, $content)) {
            $this->success[] = "Database configuration updated successfully";
            return true;
        } else {
            $this->errors[] = "Failed to update database configuration";
            return false;
        }
    }
    
    public function checkConfigurationFiles() {
        $config_files = [
            'config.php' => 'Main configuration',
            'database.php' => 'Database configuration',
            'email.php' => 'Email configuration',
            'email_templates.php' => 'Email templates'
        ];
        
        $results = [];
        
        foreach ($config_files as $file => $description) {
            $path = $this->config_path . '/' . $file;
            $exists = file_exists($path);
            $results[$file] = [
                'exists' => $exists,
                'description' => $description,
                'readable' => $exists ? is_readable($path) : false,
                'writable' => $exists ? is_writable($path) : false
            ];
            
            if (!$exists) {
                $this->errors[] = "Configuration file missing: $file";
            }
        }
        
        return $results;
    }
    
    public function checkProductionSettings() {
        $config_file = $this->config_path . '/config.php';
        
        if (!file_exists($config_file)) {
            $this->errors[] = "Main configuration file not found";
            return false;
        }
        
        $content = file_get_contents($config_file);
        $settings = [];
        
        // Check error reporting
        if (strpos($content, 'error_reporting(0)') !== false) {
            $settings['error_reporting'] = true;
        } else {
            $settings['error_reporting'] = false;
            $this->warnings[] = "Error reporting should be disabled in production";
        }
        
        // Check debug mode
        if (strpos($content, "define('DEBUG_MODE', false)") !== false) {
            $settings['debug_mode'] = true;
        } else {
            $settings['debug_mode'] = false;
            $this->warnings[] = "Debug mode should be disabled in production";
        }
        
        // Check environment
        if (strpos($content, "define('ENVIRONMENT', 'production')") !== false) {
            $settings['environment'] = true;
        } else {
            $settings['environment'] = false;
            $this->warnings[] = "Environment should be set to 'production'";
        }
        
        return $settings;
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getWarnings() {
        return $this->warnings;
    }
    
    public function getSuccess() {
        return $this->success;
    }
    
    public function markInstallationComplete() {
        $lock_file = __DIR__ . '/install_completed.lock';
        $content = "Installation completed on: " . date('Y-m-d H:i:s') . "\n";
        $content .= "Server: " . $_SERVER['HTTP_HOST'] . "\n";
        $content .= "IP: " . $_SERVER['SERVER_ADDR'] . "\n";
        
        return file_put_contents($lock_file, $content) !== false;
    }
}

// Initialize installer
$installer = new ProductionInstaller();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_database':
                $host = $_POST['db_host'] ?? '';
                $username = $_POST['db_username'] ?? '';
                $password = $_POST['db_password'] ?? '';
                $database = $_POST['db_name'] ?? '';
                
                // Test connection first
                $test_result = $installer->testDatabaseConnection($host, $username, $password, $database);
                
                if ($test_result['status']) {
                    $installer->updateDatabaseConfig($host, $username, $password, $database);
                } else {
                    $installer->getErrors()[] = $test_result['message'];
                }
                break;
                
            case 'complete_installation':
                $installer->markInstallationComplete();
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
                break;
        }
    }
}

// Get current status
$folder_check = $installer->checkProductionFolder();
$dev_files_check = $installer->checkDevelopmentFiles();
$current_db_config = $installer->getCurrentDatabaseConfig();
$config_files = $installer->checkConfigurationFiles();
$production_settings = $installer->checkProductionSettings();

// Test current database connection if config exists
$db_connection_status = false;
if ($current_db_config) {
    $db_test = $installer->testDatabaseConnection(
        $current_db_config['host'] ?? '',
        $current_db_config['username'] ?? '',
        $current_db_config['password'] ?? '',
        $current_db_config['database'] ?? ''
    );
    $db_connection_status = $db_test['status'];
    $db_connection_message = $db_test['message'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Banking - Production Configuration</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 15px 15px 0 0; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; font-size: 2.5em; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 1.2em; }
        .main-content { background: white; padding: 0; border-radius: 0 0 15px 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .tabs { display: flex; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .tab { flex: 1; padding: 15px 20px; background: #f8f9fa; border: none; cursor: pointer; font-size: 16px; font-weight: 500; color: #495057; transition: all 0.3s; }
        .tab.active { background: white; color: #2c3e50; border-bottom: 3px solid #3498db; }
        .tab:hover { background: #e9ecef; }
        .tab-content { display: none; padding: 30px; }
        .tab-content.active { display: block; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .status-card { background: #f8f9fa; border-radius: 10px; padding: 20px; border-left: 4px solid #dee2e6; }
        .status-card.success { border-left-color: #28a745; background: #d4edda; }
        .status-card.warning { border-left-color: #ffc107; background: #fff3cd; }
        .status-card.error { border-left-color: #dc3545; background: #f8d7da; }
        .status-card h3 { margin-bottom: 10px; color: #2c3e50; }
        .form-group { margin-bottom: 20px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 600; color: #2c3e50; }
        input[type="text"], input[type="password"], select { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .btn { background: #3498db; color: white; padding: 12px 25px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: 500; transition: all 0.3s; }
        .btn:hover { background: #2980b9; transform: translateY(-1px); }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #218838; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        .checklist { list-style: none; }
        .checklist li { padding: 10px 0; border-bottom: 1px solid #eee; display: flex; align-items: center; }
        .checklist li:last-child { border-bottom: none; }
        .check-icon { margin-right: 10px; font-size: 18px; }
        .check-icon.success { color: #28a745; }
        .check-icon.error { color: #dc3545; }
        .check-icon.warning { color: #ffc107; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .progress-bar { background: #e9ecef; border-radius: 10px; height: 20px; margin: 20px 0; overflow: hidden; }
        .progress-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; transition: width 0.5s ease; }
        .file-list { max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .security-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 20px; border-radius: 10px; margin-bottom: 30px; }
        .security-warning h3 { margin-bottom: 15px; color: #856404; }
        .final-steps { background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 20px; border-radius: 10px; margin-top: 30px; }
        .final-steps h3 { margin-bottom: 15px; color: #004085; }
        .final-steps ol { margin-left: 20px; }
        .final-steps li { margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 Online Banking System</h1>
            <p>Production Deployment Configuration & Verification Tool</p>
        </div>

        <div class="main-content">
            <div class="tabs">
                <button class="tab active" onclick="showTab('overview')">📊 Overview</button>
                <button class="tab" onclick="showTab('database')">🗄️ Database</button>
                <button class="tab" onclick="showTab('files')">📁 Files</button>
                <button class="tab" onclick="showTab('config')">⚙️ Configuration</button>
                <button class="tab" onclick="showTab('complete')">✅ Complete</button>
            </div>

            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <h2>🎯 Deployment Status Overview</h2>

                <?php
                // Calculate overall progress
                $total_checks = 5;
                $passed_checks = 0;

                if ($folder_check['status']) $passed_checks++;
                if ($dev_files_check['status']) $passed_checks++;
                if ($db_connection_status) $passed_checks++;
                if ($config_files['config.php']['exists']) $passed_checks++;
                if ($production_settings && $production_settings['debug_mode']) $passed_checks++;

                $progress_percentage = ($passed_checks / $total_checks) * 100;
                ?>

                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%"></div>
                </div>
                <p style="text-align: center; margin-bottom: 30px;">
                    <strong>Overall Progress: <?php echo round($progress_percentage); ?>% Complete</strong>
                    (<?php echo $passed_checks; ?>/<?php echo $total_checks; ?> checks passed)
                </p>

                <div class="status-grid">
                    <div class="status-card <?php echo $folder_check['status'] ? 'success' : 'error'; ?>">
                        <h3><?php echo $folder_check['status'] ? '✅' : '❌'; ?> Production Files</h3>
                        <p><?php echo $folder_check['status'] ? 'All required files and directories are present' : 'Some required files or directories are missing'; ?></p>
                    </div>

                    <div class="status-card <?php echo $dev_files_check['status'] ? 'success' : 'warning'; ?>">
                        <h3><?php echo $dev_files_check['status'] ? '✅' : '⚠️'; ?> Development Files</h3>
                        <p><?php echo $dev_files_check['status'] ? 'No development files found in production' : 'Development files detected'; ?></p>
                    </div>

                    <div class="status-card <?php echo $db_connection_status ? 'success' : 'error'; ?>">
                        <h3><?php echo $db_connection_status ? '✅' : '❌'; ?> Database Connection</h3>
                        <p><?php echo $db_connection_status ? 'Database connected successfully' : 'Database connection failed or not configured'; ?></p>
                    </div>

                    <div class="status-card <?php echo ($production_settings && $production_settings['debug_mode']) ? 'success' : 'warning'; ?>">
                        <h3><?php echo ($production_settings && $production_settings['debug_mode']) ? '✅' : '⚠️'; ?> Production Settings</h3>
                        <p><?php echo ($production_settings && $production_settings['debug_mode']) ? 'Production settings configured correctly' : 'Production settings need adjustment'; ?></p>
                    </div>
                </div>

                <?php if (!empty($installer->getErrors())): ?>
                    <div class="alert error">
                        <h3>❌ Errors Found:</h3>
                        <ul>
                            <?php foreach ($installer->getErrors() as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($installer->getWarnings())): ?>
                    <div class="alert warning">
                        <h3>⚠️ Warnings:</h3>
                        <ul>
                            <?php foreach ($installer->getWarnings() as $warning): ?>
                                <li><?php echo htmlspecialchars($warning); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($installer->getSuccess())): ?>
                    <div class="alert success">
                        <h3>✅ Success:</h3>
                        <ul>
                            <?php foreach ($installer->getSuccess() as $success): ?>
                                <li><?php echo htmlspecialchars($success); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Database Tab -->
            <div id="database" class="tab-content">
                <h2>🗄️ Database Configuration</h2>

                <div class="security-warning">
                    <h3>🔒 Security Notice</h3>
                    <p>Ensure you have already imported the database schema using phpMyAdmin or MySQL command line.
                    This tool will only configure the connection settings and verify the database structure.</p>
                </div>

                <?php if ($current_db_config): ?>
                    <div class="status-card <?php echo $db_connection_status ? 'success' : 'error'; ?>">
                        <h3>Current Database Configuration</h3>
                        <p><strong>Host:</strong> <?php echo htmlspecialchars($current_db_config['host'] ?? 'Not set'); ?></p>
                        <p><strong>Database:</strong> <?php echo htmlspecialchars($current_db_config['database'] ?? 'Not set'); ?></p>
                        <p><strong>Username:</strong> <?php echo htmlspecialchars($current_db_config['username'] ?? 'Not set'); ?></p>
                        <p><strong>Status:</strong>
                            <span style="color: <?php echo $db_connection_status ? '#28a745' : '#dc3545'; ?>">
                                <?php echo $db_connection_status ? '✅ Connected' : '❌ ' . ($db_connection_message ?? 'Connection failed'); ?>
                            </span>
                        </p>
                    </div>
                <?php endif; ?>

                <form method="POST" style="margin-top: 30px;">
                    <input type="hidden" name="action" value="update_database">

                    <h3>Update Database Configuration</h3>
                    <p style="margin-bottom: 20px; color: #6c757d;">
                        Enter your production database credentials. The connection will be tested before saving.
                    </p>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_host">Database Host:</label>
                            <input type="text" id="db_host" name="db_host"
                                   value="<?php echo htmlspecialchars($current_db_config['host'] ?? 'localhost'); ?>"
                                   placeholder="localhost" required>
                        </div>

                        <div class="form-group">
                            <label for="db_name">Database Name:</label>
                            <input type="text" id="db_name" name="db_name"
                                   value="<?php echo htmlspecialchars($current_db_config['database'] ?? ''); ?>"
                                   placeholder="your_bank_database" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_username">Database Username:</label>
                            <input type="text" id="db_username" name="db_username"
                                   value="<?php echo htmlspecialchars($current_db_config['username'] ?? ''); ?>"
                                   placeholder="database_user" required>
                        </div>

                        <div class="form-group">
                            <label for="db_password">Database Password:</label>
                            <input type="password" id="db_password" name="db_password"
                                   value="<?php echo htmlspecialchars($current_db_config['password'] ?? ''); ?>"
                                   placeholder="Enter database password" required>
                        </div>
                    </div>

                    <button type="submit" class="btn">🔄 Test & Update Database Configuration</button>
                </form>

                <div class="final-steps">
                    <h3>📋 Database Setup Checklist</h3>
                    <ol>
                        <li>Create a new database for your banking system</li>
                        <li>Create a database user with SELECT, INSERT, UPDATE, DELETE privileges</li>
                        <li>Import all SQL files from the database/ folder:
                            <ul style="margin-top: 10px;">
                                <li>schema.sql (main database structure)</li>
                                <li>create_otp_table.sql</li>
                                <li>create_super_admin_2fa_table.sql</li>
                                <li>create_super_admin_settings.sql</li>
                                <li>create_user_security_settings.sql</li>
                                <li>create_user_documents_table.sql</li>
                                <li>secure_deletion_tables.sql</li>
                                <li>add_user_fields.sql</li>
                            </ul>
                        </li>
                        <li>Update the database configuration above</li>
                        <li>Test the connection</li>
                    </ol>
                </div>
            </div>

            <!-- Files Tab -->
            <div id="files" class="tab-content">
                <h2>📁 File Integrity Verification</h2>

                <div class="status-grid">
                    <div class="status-card">
                        <h3>📂 Required Directories</h3>
                        <ul class="checklist">
                            <?php foreach ($folder_check['dirs'] as $dir => $exists): ?>
                                <li>
                                    <span class="check-icon <?php echo $exists ? 'success' : 'error'; ?>">
                                        <?php echo $exists ? '✅' : '❌'; ?>
                                    </span>
                                    <?php echo htmlspecialchars($dir); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="status-card">
                        <h3>📄 Required Files</h3>
                        <ul class="checklist">
                            <?php foreach ($folder_check['files'] as $file => $exists): ?>
                                <li>
                                    <span class="check-icon <?php echo $exists ? 'success' : 'error'; ?>">
                                        <?php echo $exists ? '✅' : '❌'; ?>
                                    </span>
                                    <?php echo htmlspecialchars($file); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <div class="status-card <?php echo $dev_files_check['status'] ? 'success' : 'warning'; ?>">
                    <h3>🧹 Development Files Check</h3>
                    <?php if ($dev_files_check['status']): ?>
                        <p>✅ No development files found in production folder. Good!</p>
                    <?php else: ?>
                        <p>⚠️ Development files detected in production folder:</p>
                        <div class="file-list">
                            <?php foreach ($dev_files_check['files'] as $file): ?>
                                <div><?php echo htmlspecialchars(basename($file)); ?></div>
                            <?php endforeach; ?>
                        </div>
                        <p style="margin-top: 15px; color: #856404;">
                            <strong>Recommendation:</strong> Remove these files from the production folder for security.
                        </p>
                    <?php endif; ?>
                </div>

                <div class="final-steps">
                    <h3>📋 File Deployment Checklist</h3>
                    <ol>
                        <li>Upload the entire production/ folder to your web server</li>
                        <li>Ensure all required directories are present</li>
                        <li>Verify no development/test files are included</li>
                        <li>Set proper file permissions:
                            <ul style="margin-top: 10px;">
                                <li>Files: 644 (readable by web server)</li>
                                <li>Directories: 755 (executable by web server)</li>
                                <li>Config files: 600 (secure, readable only by owner)</li>
                                <li>Upload directories: 755 (writable by web server)</li>
                            </ul>
                        </li>
                        <li>Ensure uploads/ and logs/ directories are writable</li>
                    </ol>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div id="config" class="tab-content">
                <h2>⚙️ Configuration Verification</h2>

                <div class="status-grid">
                    <div class="status-card">
                        <h3>📋 Configuration Files</h3>
                        <ul class="checklist">
                            <?php foreach ($config_files as $file => $info): ?>
                                <li>
                                    <span class="check-icon <?php echo $info['exists'] ? 'success' : 'error'; ?>">
                                        <?php echo $info['exists'] ? '✅' : '❌'; ?>
                                    </span>
                                    <div>
                                        <strong><?php echo htmlspecialchars($file); ?></strong><br>
                                        <small><?php echo htmlspecialchars($info['description']); ?></small>
                                        <?php if ($info['exists']): ?>
                                            <br><small style="color: #6c757d;">
                                                Readable: <?php echo $info['readable'] ? '✅' : '❌'; ?> |
                                                Writable: <?php echo $info['writable'] ? '✅' : '❌'; ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="status-card">
                        <h3>🔧 Production Settings</h3>
                        <?php if ($production_settings): ?>
                            <ul class="checklist">
                                <li>
                                    <span class="check-icon <?php echo $production_settings['error_reporting'] ? 'success' : 'warning'; ?>">
                                        <?php echo $production_settings['error_reporting'] ? '✅' : '⚠️'; ?>
                                    </span>
                                    Error Reporting Disabled
                                </li>
                                <li>
                                    <span class="check-icon <?php echo $production_settings['debug_mode'] ? 'success' : 'warning'; ?>">
                                        <?php echo $production_settings['debug_mode'] ? '✅' : '⚠️'; ?>
                                    </span>
                                    Debug Mode Disabled
                                </li>
                                <li>
                                    <span class="check-icon <?php echo $production_settings['environment'] ? 'success' : 'warning'; ?>">
                                        <?php echo $production_settings['environment'] ? '✅' : '⚠️'; ?>
                                    </span>
                                    Environment Set to Production
                                </li>
                            </ul>
                        <?php else: ?>
                            <p style="color: #dc3545;">❌ Unable to verify production settings</p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="final-steps">
                    <h3>📋 Configuration Checklist</h3>
                    <ol>
                        <li><strong>config/config.php:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Update BASE_URL to your production domain</li>
                                <li>Update APP_URL to your production domain</li>
                                <li>Update ADMIN_EMAIL to your production email</li>
                                <li>Ensure DEBUG_MODE is false</li>
                                <li>Ensure error_reporting(0) is set</li>
                            </ul>
                        </li>
                        <li><strong>config/database.php:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Update database credentials (use Database tab)</li>
                                <li>Test database connection</li>
                            </ul>
                        </li>
                        <li><strong>config/email.php:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Configure SMTP server settings</li>
                                <li>Update FROM_EMAIL and FROM_NAME</li>
                                <li>Test email functionality</li>
                            </ul>
                        </li>
                        <li><strong>Web Server Configuration:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Configure SSL certificate</li>
                                <li>Set up HTTPS redirect</li>
                                <li>Protect sensitive directories (config/, logs/)</li>
                                <li>Configure proper error pages</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>

            <!-- Complete Tab -->
            <div id="complete" class="tab-content">
                <h2>✅ Complete Installation</h2>

                <?php
                $all_checks_passed = $folder_check['status'] &&
                                   $dev_files_check['status'] &&
                                   $db_connection_status &&
                                   $config_files['config.php']['exists'] &&
                                   ($production_settings && $production_settings['debug_mode']);
                ?>

                <?php if ($all_checks_passed): ?>
                    <div class="alert success">
                        <h3>🎉 Ready for Production!</h3>
                        <p>All checks have passed successfully. Your Online Banking System is ready for production deployment.</p>
                    </div>

                    <div class="security-warning">
                        <h3>🔒 Critical Security Steps</h3>
                        <p><strong>Before marking installation complete, ensure you have:</strong></p>
                        <ul style="margin-top: 15px;">
                            <li>✅ Changed all default admin passwords</li>
                            <li>✅ Configured SSL certificate and HTTPS</li>
                            <li>✅ Updated all domain URLs in configuration</li>
                            <li>✅ Configured production SMTP settings</li>
                            <li>✅ Set up proper file permissions</li>
                            <li>✅ Tested all major functionality</li>
                        </ul>
                    </div>

                    <form method="POST" style="margin-top: 30px;">
                        <input type="hidden" name="action" value="complete_installation">
                        <button type="submit" class="btn success" onclick="return confirm('Are you sure you want to complete the installation? This will lock the installer for security.')">
                            🔒 Mark Installation Complete & Lock Installer
                        </button>
                    </form>

                <?php else: ?>
                    <div class="alert warning">
                        <h3>⚠️ Installation Not Ready</h3>
                        <p>Some checks have not passed yet. Please complete all required steps before finalizing the installation.</p>
                    </div>

                    <div class="status-card error">
                        <h3>❌ Remaining Issues</h3>
                        <ul>
                            <?php if (!$folder_check['status']): ?>
                                <li>Required files or directories are missing</li>
                            <?php endif; ?>
                            <?php if (!$dev_files_check['status']): ?>
                                <li>Development files detected in production folder</li>
                            <?php endif; ?>
                            <?php if (!$db_connection_status): ?>
                                <li>Database connection not configured or failing</li>
                            <?php endif; ?>
                            <?php if (!$config_files['config.php']['exists']): ?>
                                <li>Main configuration file missing</li>
                            <?php endif; ?>
                            <?php if (!($production_settings && $production_settings['debug_mode'])): ?>
                                <li>Production settings not properly configured</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="final-steps">
                    <h3>🚀 Post-Installation Steps</h3>
                    <p>After completing the installation, you should:</p>
                    <ol>
                        <li><strong>Change Default Credentials:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Super Admin: superadmin / Admin@123</li>
                                <li>Admin: admin / admin123</li>
                            </ul>
                        </li>
                        <li><strong>Access Points:</strong>
                            <ul style="margin-top: 10px;">
                                <li>User Portal: https://yourdomain.com/</li>
                                <li>Admin Panel: https://yourdomain.com/admin/</li>
                                <li>Super Admin: https://yourdomain.com/super-admin/</li>
                            </ul>
                        </li>
                        <li><strong>Security:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Remove or secure the install/ directory</li>
                                <li>Set up regular database backups</li>
                                <li>Configure monitoring and logging</li>
                                <li>Perform security audit</li>
                            </ul>
                        </li>
                        <li><strong>Testing:</strong>
                            <ul style="margin-top: 10px;">
                                <li>Test user registration and login</li>
                                <li>Test admin functionality</li>
                                <li>Test email sending</li>
                                <li>Test all major features</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Auto-refresh page every 30 seconds to update status
        setTimeout(() => {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
