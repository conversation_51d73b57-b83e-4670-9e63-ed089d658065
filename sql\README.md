# SQL Export System - Online Banking Database

This directory contains SQL export files and tools for the Online Banking System database.

## 📁 Files Overview

### Generated SQL Files
- `full_database_backup.sql` - Complete database backup (structure + data)
- `database_schema_only.sql` - Database structure only (no data)
- `database_data_only.sql` - Data only (no structure)
- `full_backup_YYYY-MM-DD_HH-MM-SS.sql` - Timestamped full backups

### Export Tools
- `../sql_export_generator.php` - PHP script for generating SQL exports
- `../sql_export_interface.php` - Web interface for the export generator

## 🚀 Usage

### Command Line Usage
```bash
php sql_export_generator.php
```

### Web Interface
Open in browser: `http://localhost/online_banking/sql_export_interface.php`

### Direct API Calls
```php
// Full backup
GET/POST: sql_export_generator.php?action=full_backup

// Schema only
GET/POST: sql_export_generator.php?action=schema_backup

// Data only
GET/POST: sql_export_generator.php?action=data_backup

// List exports
GET: sql_export_generator.php?action=list_exports

// Get database stats
GET: sql_export_generator.php?action=get_stats

// Get table list
GET: sql_export_generator.php?action=get_tables
```

## 📊 Database Statistics

- **Tables:** 36 tables
- **Users:** 15 total (1 Super Admin, 1 Admin, 13 Users)
- **Transactions:** 17 transactions totaling $8,065,435.00
- **Virtual Cards:** 5 active cards
- **Audit Logs:** 247+ entries

## 🗄️ Key Tables

### Core Tables
- `accounts` - User accounts and authentication
- `transactions` - Financial transactions
- `transfers` - Money transfers between accounts
- `virtual_cards` - Digital card management

### Security Tables
- `user_security_settings` - 2FA and security preferences
- `user_otps` - OTP management
- `super_admin_2fa_settings` - Google Authenticator settings
- `audit_logs` - System activity logging

### Administrative Tables
- `super_admin_settings` - System configuration
- `system_settings` - Application settings
- `email_logs` - Email delivery tracking
- `user_documents` - KYC document management

## 🔧 Export Types

### 1. Full Backup
- Complete database structure and data
- Includes triggers, routines, and constraints
- Best for complete system restoration

### 2. Schema Only
- Database structure without data
- Useful for setting up new environments
- Includes table definitions, indexes, and relationships

### 3. Data Only
- Data without structure
- Useful for data migration
- Requires existing database structure

### 4. Table-Specific Export
- Export selected tables only
- Customizable table selection
- Useful for partial backups

### 5. Custom Query Export
- Execute custom SQL queries
- Export query results
- Useful for reporting and analysis

## 🛡️ Security Notes

- SQL files may contain sensitive data
- Store exports in secure locations
- Use appropriate file permissions
- Consider encryption for sensitive exports

## 📝 Restore Instructions

### Full Database Restore
```bash
mysql -u root -proot online_banking < full_database_backup.sql
```

### Schema Only Restore
```bash
mysql -u root -proot online_banking < database_schema_only.sql
```

### Data Only Restore
```bash
mysql -u root -proot online_banking < database_data_only.sql
```

## 🔄 Automated Backups

Consider setting up automated backups using:
- Cron jobs (Linux/Mac)
- Task Scheduler (Windows)
- Database backup tools

Example cron job (daily backup at 2 AM):
```bash
0 2 * * * cd /path/to/online_banking && php sql_export_generator.php
```

## 📞 Support

For issues with the export system:
1. Check database connection settings in `config/database.php`
2. Verify mysqldump is accessible
3. Check file permissions on the sql/ directory
4. Review error logs for detailed error messages

## 🔗 Related Files

- `config/database.php` - Database configuration
- `database/schema.sql` - Original database schema
- `database/` - Additional database setup files

---

**Generated:** <?php echo date('Y-m-d H:i:s'); ?>
**System:** Online Banking System v1.0
