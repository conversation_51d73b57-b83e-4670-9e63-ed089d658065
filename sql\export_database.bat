@echo off
REM SQL Export Batch Script for Online Banking System
REM This script provides easy access to database export functions

title Online Banking - SQL Export Tool

echo.
echo ========================================
echo   Online Banking SQL Export Tool
echo ========================================
echo.

REM Check if PHP is available
php --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PHP is not found in PATH
    echo Please make sure PHP is installed and added to PATH
    echo.
    echo For MAMP users, PHP is typically located at:
    echo C:\MAMP\bin\php\php8.x.x\php.exe
    echo.
    pause
    exit /b 1
)

REM Navigate to the parent directory (where the PHP script is located)
cd /d "%~dp0.."

echo Current directory: %CD%
echo.

:MENU
echo Choose an export option:
echo.
echo 1. Full Database Backup (Structure + Data)
echo 2. Schema Only (Structure)
echo 3. Data Only
echo 4. List Existing Exports
echo 5. Database Statistics
echo 6. Open Web Interface
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto FULL_BACKUP
if "%choice%"=="2" goto SCHEMA_BACKUP
if "%choice%"=="3" goto DATA_BACKUP
if "%choice%"=="4" goto LIST_EXPORTS
if "%choice%"=="5" goto DB_STATS
if "%choice%"=="6" goto WEB_INTERFACE
if "%choice%"=="7" goto EXIT
goto INVALID_CHOICE

:FULL_BACKUP
echo.
echo Generating full database backup...
echo.
php sql_export_generator.php
echo.
goto CONTINUE

:SCHEMA_BACKUP
echo.
echo Generating schema-only backup...
echo.
echo 2 | php sql_export_generator.php
echo.
goto CONTINUE

:DATA_BACKUP
echo.
echo Generating data-only backup...
echo.
echo 3 | php sql_export_generator.php
echo.
goto CONTINUE

:LIST_EXPORTS
echo.
echo Listing existing exports...
echo.
echo 4 | php sql_export_generator.php
echo.
goto CONTINUE

:DB_STATS
echo.
echo Database statistics...
echo.
echo 5 | php sql_export_generator.php
echo.
goto CONTINUE

:WEB_INTERFACE
echo.
echo Opening web interface...
echo.
start http://localhost/online_banking/sql_export_interface.php
echo Web interface opened in your default browser.
echo.
goto CONTINUE

:INVALID_CHOICE
echo.
echo Invalid choice. Please select 1-7.
echo.
goto MENU

:CONTINUE
echo.
echo Press any key to return to menu...
pause >nul
cls
goto MENU

:EXIT
echo.
echo Thank you for using the SQL Export Tool!
echo.
pause
exit /b 0
