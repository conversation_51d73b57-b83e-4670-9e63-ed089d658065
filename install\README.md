# Online Banking System - Installation Tool

This directory contains the production deployment configuration and verification tool for the Online Banking System.

## 🚀 Quick Start

1. **Upload Production Files**: Upload the entire `production/` folder to your web server
2. **Import Database**: Import all SQL files from `production/database/` into your MySQL database
3. **Access Installer**: Navigate to `https://yourdomain.com/install/install.php`
4. **Enter Password**: Use the default password `banking_install_2025` (change this in install.php)
5. **Configure Database**: Update database connection settings
6. **Verify Installation**: Complete all verification steps
7. **Mark Complete**: Lock the installer when finished

## 📋 What This Tool Does

### ✅ Database Configuration
- Reads current database settings from `production/config/database.php`
- Allows updating database credentials through a secure form
- Tests database connection before saving changes
- Verifies required database tables exist

### ✅ File Integrity Verification
- Checks all required directories are present (admin/, dashboard/, config/, etc.)
- Verifies essential files exist (index.php, login.php, etc.)
- Scans for development files that shouldn't be in production
- Displays comprehensive file status checklist

### ✅ Configuration Validation
- Verifies production settings (DEBUG_MODE = false, error_reporting = 0)
- Checks configuration file permissions
- Validates email configuration structure
- Ensures logs/ and uploads/ directories exist

### ✅ Deployment Status Dashboard
- Shows overall installation progress percentage
- Displays color-coded status for each component
- Provides step-by-step completion checklist
- Shows "Ready for Production" when all checks pass

### ✅ Security Features
- Password-protected access to installer
- Auto-disables after successful configuration
- Displays warnings about changing default credentials
- Provides next steps for SSL and domain setup

## 🔧 Installation Process

### Prerequisites
Before using this tool, ensure you have:
- ✅ Uploaded the production folder to your web server
- ✅ Created a MySQL database for the banking system
- ✅ Imported all SQL files from `production/database/`
- ✅ Configured basic web server settings

### Step-by-Step Process

#### 1. Access the Installer
Navigate to: `https://yourdomain.com/install/install.php`

#### 2. Authentication
- Default password: `banking_install_2025`
- **Security Note**: Change this password in `install.php` before deployment

#### 3. Overview Tab
- Review overall installation status
- Check progress percentage
- View any errors or warnings

#### 4. Database Tab
- View current database configuration
- Update database credentials
- Test database connection
- Verify required tables exist

#### 5. Files Tab
- Verify all required files and directories
- Check for development files in production
- Review file permission requirements

#### 6. Configuration Tab
- Verify production settings are applied
- Check configuration file status
- Review manual configuration steps

#### 7. Complete Tab
- Final verification of all components
- Mark installation as complete
- Lock installer for security

## 🛡️ Security Considerations

### Password Protection
The installer is protected with a password defined in the file:
```php
define('INSTALL_PASSWORD', 'banking_install_2025'); // Change this!
```

### Auto-Disable Feature
After marking installation complete, the tool creates `install_completed.lock` and becomes inaccessible.

### File Permissions
Ensure proper permissions:
- Install directory: 755
- install.php: 644
- Configuration files: 600

## 📁 Files in This Directory

- **`install.php`** - Main installation interface
- **`config_helper.php`** - Configuration utility functions
- **`README.md`** - This documentation file
- **`install_completed.lock`** - Created when installation is complete (auto-generated)

## ⚙️ Configuration Requirements

### Database Configuration
Update these settings in the Database tab:
- Database Host (usually `localhost`)
- Database Name (your banking database)
- Database Username (with SELECT, INSERT, UPDATE, DELETE privileges)
- Database Password (secure password)

### Required Database Tables
The installer verifies these tables exist:
- `accounts` - User accounts
- `transactions` - Financial transactions
- `transfers` - Money transfers
- `virtual_cards` - Virtual card system

### Production Settings
The installer checks these configuration settings:
- `DEBUG_MODE = false` in config/config.php
- `error_reporting(0)` in config/config.php
- `ENVIRONMENT = 'production'` in config/config.php

## 🚨 Default Credentials (CHANGE IMMEDIATELY)

After installation, change these default credentials:

### Super Admin
- **URL**: `https://yourdomain.com/super-admin/`
- **Username**: `superadmin`
- **Password**: `Admin@123`

### Admin
- **URL**: `https://yourdomain.com/admin/`
- **Username**: `admin`
- **Password**: `admin123`

## 📋 Post-Installation Checklist

After completing the installation:

### Immediate Actions
- [ ] Change all default passwords
- [ ] Update domain URLs in config/config.php
- [ ] Configure SMTP settings in config/email.php
- [ ] Test user registration and login
- [ ] Test admin panel functionality
- [ ] Verify email sending works

### Security Setup
- [ ] Install SSL certificate
- [ ] Configure HTTPS redirect
- [ ] Set proper file permissions
- [ ] Protect sensitive directories
- [ ] Remove or secure install/ directory

### System Configuration
- [ ] Set up database backups
- [ ] Configure error logging
- [ ] Set up monitoring
- [ ] Test all major features
- [ ] Perform security audit

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Failed
- Verify database credentials are correct
- Ensure database user has proper privileges
- Check if database server is running
- Verify database name exists

#### Missing Files/Directories
- Re-upload production folder completely
- Check file permissions
- Verify web server configuration

#### Configuration File Errors
- Check file permissions (config files should be 600)
- Verify PHP syntax in configuration files
- Ensure web server can read configuration directory

#### Permission Denied Errors
- Set proper file permissions (644 for files, 755 for directories)
- Ensure web server user owns the files
- Check uploads/ and logs/ directory permissions

### Getting Help
If you encounter issues:
1. Check the error messages in the installer
2. Review web server error logs
3. Verify all prerequisites are met
4. Ensure database schema is properly imported

## 🔒 Security Warning

**Important**: After completing the installation:
1. Remove the install/ directory entirely, OR
2. Rename it to something non-obvious, OR
3. Protect it with additional authentication

The installer contains sensitive configuration tools and should not be accessible in production.

---

**Installation Tool Version**: 1.2.0  
**Compatible With**: Online Banking System v1.2.0  
**Last Updated**: 2025-07-10
