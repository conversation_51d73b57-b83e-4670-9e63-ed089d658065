#!/bin/bash
# production-deploy.sh - Automated deployment script for Online Banking System
# Version: 1.2.0
# Date: 2025-07-10

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration - MODIFY THESE VALUES
SOURCE_DIR="/path/to/development/online_banking"
DEST_DIR="/var/www/html/banking"
DB_NAME="banking_production"
DB_USER="banking_user"
DB_PASS="CHANGE_THIS_PASSWORD"
DOMAIN_NAME="yourdomain.com"
ADMIN_EMAIL="<EMAIL>"

# Function to print colored output
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}   Online Banking Production Deploy${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root or with sudo"
        exit 1
    fi
    
    # Check if source directory exists
    if [ ! -d "$SOURCE_DIR" ]; then
        print_error "Source directory not found: $SOURCE_DIR"
        print_info "Please update SOURCE_DIR in the script configuration"
        exit 1
    fi
    
    # Check required commands
    local commands=("mysql" "php" "rsync" "tar")
    for cmd in "${commands[@]}"; do
        if ! command -v $cmd &> /dev/null; then
            print_error "$cmd is not installed"
            exit 1
        fi
    done
    
    print_success "Prerequisites check passed"
}

# Function to backup existing installation
backup_existing() {
    if [ -d "$DEST_DIR" ]; then
        print_info "Backing up existing installation..."
        local backup_file="/tmp/banking_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
        tar -czf "$backup_file" -C "$(dirname $DEST_DIR)" "$(basename $DEST_DIR)" 2>/dev/null || true
        print_success "Backup created: $backup_file"
    fi
}

# Function to create directory structure
create_directories() {
    print_info "Creating directory structure..."
    
    mkdir -p $DEST_DIR/{uploads/{documents,cheques,temp},logs}
    
    print_success "Directory structure created"
}

# Function to copy application files
copy_files() {
    print_info "Copying application files..."
    
    # Copy files excluding development artifacts
    rsync -av --delete \
        --exclude='test/' \
        --exclude='*_test.php' \
        --exclude='test_*.php' \
        --exclude='debug*.php' \
        --exclude='*.md' \
        --exclude='demo-images/' \
        --exclude='sql/' \
        --exclude='.git/' \
        --exclude='logs/*.log' \
        --exclude='uploads/documents/*' \
        --exclude='uploads/cheques/*' \
        --exclude='uploads/temp/*' \
        $SOURCE_DIR/ $DEST_DIR/
    
    print_success "Application files copied"
}

# Function to install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    
    cd $DEST_DIR
    
    # Install composer dependencies for production
    if [ -f "composer.phar" ]; then
        php composer.phar install --no-dev --optimize-autoloader --no-interaction
    elif command -v composer &> /dev/null; then
        composer install --no-dev --optimize-autoloader --no-interaction
    else
        print_warning "Composer not found, skipping dependency installation"
    fi
    
    print_success "Dependencies installed"
}

# Function to set file permissions
set_permissions() {
    print_info "Setting file permissions..."
    
    # Set general permissions
    find $DEST_DIR -type f -exec chmod 644 {} \;
    find $DEST_DIR -type d -exec chmod 755 {} \;
    
    # Secure configuration files
    chmod 600 $DEST_DIR/config/*.php
    
    # Set upload directory permissions
    chmod 755 $DEST_DIR/uploads/
    chmod 755 $DEST_DIR/uploads/documents/
    chmod 755 $DEST_DIR/uploads/cheques/
    chmod 755 $DEST_DIR/uploads/temp/
    
    # Set log directory permissions
    chmod 755 $DEST_DIR/logs/
    
    # Set ownership to web server user
    if id "www-data" &>/dev/null; then
        chown -R www-data:www-data $DEST_DIR
    elif id "apache" &>/dev/null; then
        chown -R apache:apache $DEST_DIR
    elif id "nginx" &>/dev/null; then
        chown -R nginx:nginx $DEST_DIR
    else
        print_warning "Web server user not found, please set ownership manually"
    fi
    
    print_success "File permissions set"
}

# Function to setup database
setup_database() {
    print_info "Setting up database..."
    
    # Prompt for MySQL root password
    echo -n "Enter MySQL root password: "
    read -s mysql_root_pass
    echo
    
    # Create database and user
    mysql -u root -p$mysql_root_pass << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
GRANT SELECT, INSERT, UPDATE, DELETE ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    # Import database schema
    print_info "Importing database schema..."
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/schema.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_otp_table.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_super_admin_2fa_table.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_super_admin_settings.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_user_security_settings.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/create_user_documents_table.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/secure_deletion_tables.sql
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < $DEST_DIR/database/add_user_fields.sql
    
    print_success "Database setup completed"
}

# Function to update configuration files
update_config() {
    print_info "Updating configuration files..."
    
    # Backup original config files
    cp $DEST_DIR/config/config.php $DEST_DIR/config/config.php.backup
    cp $DEST_DIR/config/database.php $DEST_DIR/config/database.php.backup
    cp $DEST_DIR/config/email.php $DEST_DIR/config/email.php.backup
    
    # Update config.php
    sed -i "s/define('DEBUG_MODE', true)/define('DEBUG_MODE', false)/" $DEST_DIR/config/config.php
    sed -i "s/define('ERROR_REPORTING', true)/define('ERROR_REPORTING', false)/" $DEST_DIR/config/config.php
    sed -i "s|define('BASE_URL', 'http://localhost/online_banking/')|define('BASE_URL', 'https://$DOMAIN_NAME/')|" $DEST_DIR/config/config.php
    
    # Update database.php
    sed -i "s/define('DB_NAME', 'online_banking')/define('DB_NAME', '$DB_NAME')/" $DEST_DIR/config/database.php
    sed -i "s/define('DB_USERNAME', 'root')/define('DB_USERNAME', '$DB_USER')/" $DEST_DIR/config/database.php
    sed -i "s/define('DB_PASSWORD', 'root')/define('DB_PASSWORD', '$DB_PASS')/" $DEST_DIR/config/database.php
    
    print_success "Configuration files updated"
    print_warning "Please manually update email configuration in config/email.php"
}

# Function to create backup script
create_backup_script() {
    print_info "Creating backup script..."
    
    cat > /usr/local/bin/banking-backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/backups/banking"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p \$BACKUP_DIR

# Backup database
mysqldump -u $DB_USER -p'$DB_PASS' $DB_NAME > \$BACKUP_DIR/db_backup_\$DATE.sql

# Backup files
tar -czf \$BACKUP_DIR/files_backup_\$DATE.tar.gz -C $(dirname $DEST_DIR) $(basename $DEST_DIR)

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF
    
    chmod +x /usr/local/bin/banking-backup.sh
    
    # Add to crontab for daily backups
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/banking-backup.sh") | crontab -
    
    print_success "Backup script created and scheduled"
}

# Function to display post-deployment instructions
show_post_deployment() {
    print_header
    print_success "Deployment completed successfully!"
    echo
    print_info "Application deployed to: $DEST_DIR"
    print_info "Database created: $DB_NAME"
    print_info "Database user: $DB_USER"
    echo
    print_warning "IMPORTANT: Complete these manual steps:"
    echo "1. Update config/email.php with production SMTP settings"
    echo "2. Change default admin passwords via the application"
    echo "3. Configure web server virtual host for $DOMAIN_NAME"
    echo "4. Install and configure SSL certificate"
    echo "5. Test the application thoroughly"
    echo "6. Set up monitoring and alerting"
    echo
    print_info "Default admin credentials (CHANGE IMMEDIATELY):"
    echo "   Super Admin: superadmin / Admin@123"
    echo "   Admin: admin / admin123"
    echo
    print_info "Backup script installed: /usr/local/bin/banking-backup.sh"
    print_info "Daily backups scheduled at 2:00 AM"
    echo
    print_success "Your online banking system is ready for production!"
}

# Main deployment function
main() {
    print_header
    
    print_info "Starting deployment process..."
    print_info "Source: $SOURCE_DIR"
    print_info "Destination: $DEST_DIR"
    print_info "Database: $DB_NAME"
    print_info "Domain: $DOMAIN_NAME"
    echo
    
    # Confirm deployment
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    # Execute deployment steps
    check_prerequisites
    backup_existing
    create_directories
    copy_files
    install_dependencies
    set_permissions
    setup_database
    update_config
    create_backup_script
    show_post_deployment
}

# Run main function
main "$@"
