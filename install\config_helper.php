<?php
/**
 * Configuration Helper for Online Banking System Installation
 * This file provides utility functions for configuration management
 */

class ConfigHelper {
    
    /**
     * Update configuration file with new values
     */
    public static function updateConfigFile($file_path, $updates) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        // Backup original file
        copy($file_path, $file_path . '.backup.' . date('Y-m-d-H-i-s'));
        
        $content = file_get_contents($file_path);
        
        foreach ($updates as $key => $value) {
            // Handle different types of configuration patterns
            $patterns = [
                // define('KEY', 'value')
                "/define\('$key',\s*'[^']*'\)/",
                // define('KEY', value) - for non-string values
                "/define\('$key',\s*[^)]*\)/",
                // $config['key'] = 'value'
                "/\\\$config\['$key'\]\s*=\s*'[^']*'/",
                // $key = 'value'
                "/\\\$$key\s*=\s*'[^']*'/"
            ];
            
            $replacements = [
                "define('$key', '$value')",
                "define('$key', $value)",
                "\$config['$key'] = '$value'",
                "\$$key = '$value'"
            ];
            
            foreach ($patterns as $index => $pattern) {
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, $replacements[$index], $content);
                    break;
                }
            }
        }
        
        return file_put_contents($file_path, $content) !== false;
    }
    
    /**
     * Generate secure random password
     */
    public static function generateSecurePassword($length = 16) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return substr(str_shuffle(str_repeat($chars, ceil($length / strlen($chars)))), 0, $length);
    }
    
    /**
     * Validate email configuration
     */
    public static function validateEmailConfig($smtp_host, $smtp_port, $smtp_user, $smtp_pass) {
        // Basic validation
        if (empty($smtp_host) || empty($smtp_port) || empty($smtp_user)) {
            return ['valid' => false, 'message' => 'All SMTP fields are required'];
        }
        
        if (!filter_var($smtp_user, FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'message' => 'Invalid email format for SMTP username'];
        }
        
        if (!is_numeric($smtp_port) || $smtp_port < 1 || $smtp_port > 65535) {
            return ['valid' => false, 'message' => 'Invalid SMTP port number'];
        }
        
        return ['valid' => true, 'message' => 'Email configuration appears valid'];
    }
    
    /**
     * Check if URL is valid
     */
    public static function validateUrl($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Generate .htaccess file for Apache
     */
    public static function generateHtaccess($domain) {
        return "# Online Banking System - Production .htaccess
RewriteEngine On

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Strict-Transport-Security \"max-age=********; includeSubDomains; preload\"
    Header always set Referrer-Policy \"strict-origin-when-cross-origin\"
</IfModule>

# Deny access to sensitive files and directories
<Files \"*.log\">
    Deny from all
</Files>

<Files \"*.backup\">
    Deny from all
</Files>

<Files \"composer.*\">
    Deny from all
</Files>

<Directory \"config\">
    Deny from all
</Directory>

<Directory \"logs\">
    Deny from all
</Directory>

<Directory \"database\">
    Deny from all
</Directory>

<Directory \"install\">
    Deny from all
</Directory>

# Prevent access to PHP files in uploads directory
<Directory \"uploads\">
    <Files \"*.php\">
        Deny from all
    </Files>
</Directory>

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# Disable server signature
ServerSignature Off

# Prevent directory browsing
Options -Indexes

# File upload restrictions
<FilesMatch \"\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$\">
    <Directory \"uploads\">
        Deny from all
    </Directory>
</FilesMatch>
";
    }
    
    /**
     * Generate nginx configuration
     */
    public static function generateNginxConfig($domain, $web_root) {
        return "# Online Banking System - Production Nginx Configuration
server {
    listen 80;
    server_name $domain www.$domain;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $domain www.$domain;
    root $web_root;
    index index.php index.html;
    
    # SSL Configuration (update paths to your certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    add_header X-Content-Type-Options \"nosniff\" always;
    add_header Strict-Transport-Security \"max-age=********; includeSubDomains; preload\" always;
    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;
    
    # Deny access to sensitive files
    location ~ \\\.(log|backup|md|txt)\$ {
        deny all;
    }
    
    location /config/ {
        deny all;
    }
    
    location /logs/ {
        deny all;
    }
    
    location /database/ {
        deny all;
    }
    
    location /install/ {
        deny all;
    }
    
    # Prevent PHP execution in uploads
    location /uploads/ {
        location ~ \\\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)\$ {
            deny all;
        }
    }
    
    # PHP processing
    location ~ \\\\.php\$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
    }
    
    # Static files caching
    location ~* \\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
        add_header Vary Accept-Encoding;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=login:10m rate=5r/m;
    
    location /login.php {
        limit_req zone=login burst=3 nodelay;
        try_files \$uri =404;
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Default location
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
}
";
    }
    
    /**
     * Create installation summary report
     */
    public static function generateInstallationReport($config_data) {
        $report = "# Online Banking System - Installation Report\n\n";
        $report .= "**Installation Date:** " . date('Y-m-d H:i:s') . "\n";
        $report .= "**Server:** " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "\n";
        $report .= "**PHP Version:** " . PHP_VERSION . "\n\n";
        
        $report .= "## Configuration Summary\n\n";
        $report .= "- **Database Host:** " . ($config_data['db_host'] ?? 'Not configured') . "\n";
        $report .= "- **Database Name:** " . ($config_data['db_name'] ?? 'Not configured') . "\n";
        $report .= "- **Database User:** " . ($config_data['db_user'] ?? 'Not configured') . "\n";
        $report .= "- **Domain:** " . ($config_data['domain'] ?? 'Not configured') . "\n";
        $report .= "- **SSL Configured:** " . ($config_data['ssl'] ? 'Yes' : 'No') . "\n\n";
        
        $report .= "## Default Credentials (CHANGE IMMEDIATELY)\n\n";
        $report .= "- **Super Admin:** superadmin / Admin@123\n";
        $report .= "- **Admin:** admin / admin123\n\n";
        
        $report .= "## Access URLs\n\n";
        $domain = $config_data['domain'] ?? 'yourdomain.com';
        $report .= "- **User Portal:** https://$domain/\n";
        $report .= "- **Admin Panel:** https://$domain/admin/\n";
        $report .= "- **Super Admin:** https://$domain/super-admin/\n\n";
        
        $report .= "## Next Steps\n\n";
        $report .= "1. Change all default passwords\n";
        $report .= "2. Configure SMTP settings in config/email.php\n";
        $report .= "3. Test all functionality\n";
        $report .= "4. Set up regular backups\n";
        $report .= "5. Configure monitoring\n";
        $report .= "6. Remove or secure the install/ directory\n\n";
        
        $report .= "## Security Checklist\n\n";
        $report .= "- [ ] Default passwords changed\n";
        $report .= "- [ ] SSL certificate installed\n";
        $report .= "- [ ] SMTP configured\n";
        $report .= "- [ ] File permissions set\n";
        $report .= "- [ ] Web server configured\n";
        $report .= "- [ ] Backups configured\n";
        $report .= "- [ ] Install directory secured\n";
        
        return $report;
    }
}
?>
